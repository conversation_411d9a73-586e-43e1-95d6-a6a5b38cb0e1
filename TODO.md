1. Make in-level power ups have bloom, and gravitate toward the player. Ensure that the shield resets the shield power up to 30s remaining if the player already has a shield. Currently getting a second shield power up doesn't restore the timer.


2. Add spice- rare bonuses if you complete enough levels e.g. beat level ten and get ricochet rounds that last for that session, etc.
Other ides:
 twin fire (fires two projectiles in parallel instead of one at a time)
"Negative drop": Gas cloud that causes the ship's weapon to malfunction for 3 seconds.


3. Game balance enhancements:
A. Ensure that the EnemyTypeModifier also applies to the enemy spawn rate - i.e. a EnemyType modifer of 1.5 would not only make them 50 faster and higher HP, but also 50% more of them spawn per wave, in that environment. 
B. Adjust the other modifiers magnitude of effect as well. Currently the enemies are relatively unchanging. The waves are the same and the enemies are roughly the same strength and speed even though the environment effects appear to be working and health values are adjusted etc. in custom environments. Make the effects 50% more pronounced and noticable.
C. Reduce the amount of tokens that are rewarded on level completion by 75%.

4. Add weapon variants that can be purchased for WISH - but only one at a time can be equipped. Kinetic Boost increases damage against crystal enemies. Laser is strong against shadow enemies. They should correspond to the different enemy types. Etc. They should have different colors as well. 

Bullet Visibility System
- **Current State Assessment**: Evaluate current bullet visibility and readability
- **Implementation Tasks**:
  - Implement value-based bullet design:
    - Combine bright elements (glowing cores) with dark elements (borders, inner circles)
    - Use low-contrast backgrounds primarily in midtones
  - Optimize bullet colors (favor reds, pinks, purples over yellows/oranges) according to ammo types (flame, ice, etc. corresponding to enemy types).
  - Implement chunking patterns to group bullets into readable formations
  - Implement proper depth sorting:
    - Enemy bullets drawn over player sprites, projectiles, items, and explosions
    - Smaller, faster bullets drawn over larger, slower ones
    - Single bullets drawn over larger chunks

    - **Implementation Tasks**:
  - Add appropriate bullet splash effects on impact


5. **DONE** Improve the look of the game. Make the buttons and styling look more like a sci-fi arcade game.
   Also remove the red circle rendering around the earth enemies. It was part of the default/fallback enemy rendering code. 

5.1 Improve the rest of the UI- the item cards and HUD to have a more modern sci-fi feel.


6. Pre-flight checks:
A. Check the token rewards code to determine what we still need to do in order to test with actual test tokens, and a hot wallet on the VPS to distribute them. Ensure the game is capable of checking to see how many WISH tokens are in the user's CRYPTOCURRENCY wallet when they open the Genie Menu, prompting the user with their wallet to initiate the transaction to spend if they want to buy items, and activating the purchased items only after the transaction is completed.
B. Double-check code that saves USER PROGRESS to OrangeSDK. Not balances. Progress and scores.  
C. Set up to run on VPS using nginx (we already have other games running on the VPS with nginx) and Upload to VPS, test, fix remaining bugs. Remove debug menu code. Launch token. Go live.


10. Fix enemy weapons. Currently only the fire enemies fire projectiles. Ensure each enemy type fires a corresponding projectile type (like the weapon power ups the player can buy to change ammo types). Ensure each enemy has it's own firing patterns, ammo type, and behavior corresponding to their type. Currently all enemies travel toward the player in rows like an army, and the fire enemies fire directly at the player rather than having a firing PATTERN!!!! FIX IT!



8. **Done** Fix this bug:
handleCollisions() called
GameObject.js:95 Collision check: player (r=32) vs powerup (r=15)
GameObject.js:96   Positions: player=Vector2(615.78, 740.56), powerup=Vector2(404.23, 317.42)
GameObject.js:97   Distance: 473.07615975788696, Collision threshold: 47, Colliding: false
GameObject.js:95 Collision check: player (r=32) vs powerup (r=15)
GameObject.js:96   Positions: player=Vector2(615.78, 740.56), powerup=Vector2(463.15, 356.82)
GameObject.js:97   Distance: 412.97645401188856, Collision threshold: 47, Colliding: false
GameEngine.js:1269 handleCollisions() called
GameEngine.js:1305 Enemy projectile hit player: {damageTaken: 7, health: 0, lives: 0, destroyed: true}
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 26.214899999999442, score: 2494, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.003099999999627471, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.013700000000186265, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1601 Game data saved to Orange SDK (auto_save)
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.007099999999627471, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.0075, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset

9. **Fixed** Token distribution NaN bug - Added validation to ensure cost is a valid number before processing environment purchases. Fixed in GenieInterface.js, EnvironmentTracker.js, and server/index.js.
Token balance updated: 42500 WISH tokens
GameEngine.js:1453 Token transaction: Objectamount: 7500balanceAfter: 42500id: "tx_1756744411537_fs7yft83n"metadata: {}reason: "power_up_spread_ammo"timestamp: 1756744411537type: "spent"[[Prototype]]: Object
GameEngine.js:1608 Game data saved to Orange SDK (token_change)
GameEngine.js:1477 Power-up purchased: SPREAD_AMMO for 7500 tokens
GameEngine.js:1440 Token balance updated: NaN WISH tokens
GameEngine.js:1453 Token transaction: Objectamount: undefinedbalanceAfter: NaNid: "tx_1756744416362_hyrg8lf17"metadata: {environmentId: 'env_2_1756743681799', environmentName: 'Goblin World'}reason: "environment_purchase"timestamp: 1756744416362type: "spent"[[Prototype]]: Object
EnvironmentTracker.js:253 Environment env_2_1756743681799 purchased by user_1756530070709_p9rw2v3mc for undefined tokens




  ### 2.3 Power-Up System Redesign
- **Current State Assessment**: Evaluate current power-up mechanics
- **Implementation Tasks**:
  - Implement illusion-based power progression:
    - Small actual damage increases (e.g., 1.1x multiplier per level)
    - Significant visual upgrades to convey power
  - Enhance power-up visual feedback:
    - Increase projectile width and height with power levels
    - Add more visual details to higher-level shots
    - Implement more powerful sound effects for maxed shots

    ### 1.1 Movement System Refinement
- **Current State Assessment**: Review current movement mechanics for consistency and control
- **Implementation Tasks**:
  - Add visual enhancements to improve movement feel:
    - Afterimage trails during movement


    
Cyberpunk: Neon colors, glitch effects, futuristic elements
Dark Mode: High contrast, reduced eye strain
Retro-Futurism: 80s/90s inspired futuristic design
UIGEN X 32b

Complete the projectile type configurations for all enemy types (currently missing earth, crystal, shadow) and ensure they match the visual and behavioral characteristics of corresponding weapon variants.


Ensure that upgrading hull plating gives more hitpoints. 
Add sounds
Add more enemies
Add more sprites for animation. Enable animation.