import { InputManager } from '../input/InputManager.js';
import { PlayerShip } from '../entities/PlayerShip.js';
import { GameObjectManager } from '../utils/GameObjectManager.js';
import { LevelManager } from '../managers/LevelManager.js';
import { EnemyManager } from '../managers/EnemyManager.js';
import { BossWarpManager } from '../managers/BossWarpManager.js';
import { TokenEconomyManager } from '../managers/TokenEconomyManager.js';
import { RealityWarpManager } from '../managers/RealityWarpManager.js';
import { EnvironmentTracker } from '../managers/EnvironmentTracker.js';
import { RewardManager } from '../managers/RewardManager.js';
import { GenieInterface } from '../ui/GenieInterface.js';
import { PowerUpIndicator } from '../ui/PowerUpIndicator.js';
import { OrangeSDKManager } from '../managers/OrangeSDKManager.js';
import { HangarManager } from '../managers/HangarManager.js';
import { ConsumableManager } from '../managers/ConsumableManager.js';
import { WarpVFXSystem } from '../vfx/WarpVFXSystem.js';
import { BloomEffect } from '../vfx/BloomEffect.js';
import { ENEMY_TYPES } from '../config/gameConfig.js';

/**
 * Core Game Engine - Main game loop and state management
 * Implements fixed timestep game loop with 60 FPS target
 */
export class GameEngine {
    constructor(canvas, uiElement) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.uiElement = uiElement;
        this.stars = [];

        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = 'MENU'; // Possible values: 'MENU', 'GAME_PLAY', 'PAUSED', 'GAME_OVER'

        // Fixed timestep configuration
        this.targetFPS = 60;
        this.fixedTimeStep = 1000 / this.targetFPS; // 16.67ms per frame
        this.maxFrameTime = 250; // Maximum frame time to prevent spiral of death

        // Timing variables
        this.lastFrameTime = 0;
        this.accumulator = 0;
        this.currentTime = 0;

        // Performance tracking
        this.frameCount = 0;
        this.fpsTimer = 0;
        this.currentFPS = 0;

        // Enemy sprites
        this.enemySprites = new Map();

        // Player and wingman sprites
        this.playerSprite = null;
        this.wingmanSprite = null;

        // Managers that may be attached externally
        this.bossWarpManager = null; // Boss warp manager
        this.llmClient = null; // LLM client

        // Visual Effects Systems
        this.warpVFX = null;
        this._warpVFXRegistered = false;
        this.bloomEffect = null;

        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    async init() {

        // Set up canvas properties
        this.setupCanvas();

        // Initialize game systems (placeholder for now)
        await this.initializeSystems();

        // Start the game loop (but game won't actually run until startGameplay is called)
        this.start();

        return Promise.resolve();
    }

    setupCanvas() {
        // Set up canvas for crisp pixel rendering
        this.ctx.imageSmoothingEnabled = false;

        // Set canvas size based on container
        this.handleResize();
        window.addEventListener('resize', this.handleResize);

    }

    initializeStarfield() {
        const starCount = 200;
        for (let i = 0; i < starCount; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 1.5 + 0.5,
                speed: (Math.random() * 25) + 5 // pixels per second
            });
        }
    }

    async initializeSystems() {
        // Initialize input manager
        this.inputManager = new InputManager(this.canvas, this);

        // Initialize game object manager
        this.gameObjectManager = new GameObjectManager();

        // Initialize player ship at bottom center of screen
        const startX = this.canvas.width / 2;
        const startY = this.canvas.height - 100; // 100 pixels from bottom
        this.playerShip = new PlayerShip(startX, startY, this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Player ship is handled separately for rendering, not added to game object manager
        // to avoid double rendering with interpolation issues

        // Initialize level manager
        this.levelManager = new LevelManager(this.gameObjectManager);

        // Initialize enemy manager
        this.enemyManager = new EnemyManager(this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Load enemy sprites
        await this.loadEnemySprites();
        
        // Load player and wingman sprites
        await this.loadPlayerAndWingmanSprites();
        
        // Create token economy manager first
        this.tokenManager = new TokenEconomyManager();

        // Create environment tracker
        this.environmentTracker = new EnvironmentTracker();

        // Create reward manager
        this.rewardManager = new RewardManager(this.tokenManager);

        // Create reality warp manager with proper dependencies (LLM client now handled by backend)
        this.realityWarpManager = new RealityWarpManager(this.tokenManager, null, this.levelManager);
        this.realityWarpManager.gameEngine = this; // Add reference for environment tracking
        this.realityWarpManager.init();
        
        // Initialize boss warp manager
        this.bossWarpManager = new BossWarpManager(this.realityWarpManager, this.enemyManager);
        this.bossWarpManager.init();
        
        // Connect boss warp manager with enemy manager
        this.enemyManager.setBossWarpManager(this.bossWarpManager);
        
        // Environment service is now handled by backend API
        // Connect environment with enemy manager
        // Note: Environment will be set after it's loaded from the server

        // Initialize Orange SDK manager
        this.orangeSDKManager = new OrangeSDKManager();

        // Initialize Hangar manager (after token and SDK managers)
        this.hangarManager = new HangarManager(this.tokenManager, this.orangeSDKManager);

        // Connect hangar manager to player ship
        this.playerShip.setHangarManager(this.hangarManager);

        // Initialize Genie interface (includes hangar functionality)
        this.genieInterface = new GenieInterface(this.tokenManager, this);

        // Initialize consumable manager
        this.consumableManager = new ConsumableManager(this.tokenManager, this);

        // Initialize power-up indicator
        this.powerUpIndicator = new PowerUpIndicator();
        this.powerUpIndicator.initialize();

        // Initialize Warp VFX system (purely visual)
        this.warpVFX = new WarpVFXSystem(this.canvas, this);
        // Enable VFX Dev tools (hotkeys) in debug mode
        if (this.warpVFX && (window.AuthManager?.config?.debugMode || false)) {
            this.warpVFX.initDevControls();
        }

        // Initialize Bloom Effect system
        this.bloomEffect = new BloomEffect(this.canvas, {
            enabled: true,
            intensity: 0.6,
            threshold: 0.5,
            blurRadius: 12,
            downsample: 2
        });

        // Add bloom debug controls (B key to toggle, +/- to adjust intensity)
        this.initBloomDebugControls();


        // Set up level manager callbacks
        this.setupLevelManagerCallbacks();

        // Set up enemy manager callbacks
        this.setupEnemyManagerCallbacks();
        
        // Set up boss warp manager callbacks
        this.setupBossWarpManagerCallbacks();

        // Set up Orange SDK manager callbacks
        this.setupOrangeSDKCallbacks();

        // Set up token manager callbacks
        this.setupTokenManagerCallbacks();

        // Initialize Genie interface
        await this.genieInterface.initialize();
        this.setupGenieInterfaceCallbacks();
        
        // Synchronize weapon power-ups between GenieInterface and WeaponSystem
        this.syncWeaponPowerUps();

        // Set up default environment with the enemy manager
        this.enemyManager.setEnvironment({
            getEnvironmentType: () => 'space',
            getCurrentGameplayModifiers: () => ({}),
            getCurrentEnvironmentType: () => 'space',
            getCurrentEnvironmentName: () => 'Default Environment',
            getCurrentEnvironmentDescription: () => 'Default space environment',
            isEnemyCompatible: () => true,
            getCurrentEnvironmentHazards: () => [],
            applyEnvironmentEffects: (stats) => stats,
            update: () => {},
            render: () => {},
            resetToDefault: () => {}
        });

        // Initialize Orange SDK (async)
        this.initializeOrangeSDK();

        // Initialize starfield (fallback if environment system fails)
        this.initializeStarfield();


        // Note: Level is NOT started here - it will be started when player clicks "Start Game"



    }

    /**
     * Load enemy sprites from assets/sprites/enemies directory
     * @returns {Promise<void>}
     */
    async loadEnemySprites() {
        const spritePaths = {
            [ENEMY_TYPES.WATER]: 'assets/sprites/enemies/water.png',
            [ENEMY_TYPES.FIRE]: 'assets/sprites/enemies/fire.png',
            [ENEMY_TYPES.AIR]: 'assets/sprites/enemies/air.png',
            [ENEMY_TYPES.EARTH]: 'assets/sprites/enemies/earth.png',
            [ENEMY_TYPES.CRYSTAL]: 'assets/sprites/enemies/crystal.png',
            [ENEMY_TYPES.SHADOW]: 'assets/sprites/enemies/shadow.png'
        };

        const loadPromises = Object.entries(spritePaths).map(([enemyType, path]) => {
            return new Promise((resolve) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                
                img.onload = () => {
                    this.enemySprites.set(enemyType, img);
                    console.log(`✅ Loaded sprite for ${enemyType} enemy`);
                    resolve();
                };
                
                img.onerror = () => {
                    console.warn(`⚠️ Could not load sprite for ${enemyType} enemy at ${path}, will use fallback rendering`);
                    resolve(); // Still resolve so other sprites can load
                };
                
                img.src = path;
            });
        });

        await Promise.all(loadPromises);
        console.log('✅ All enemy sprites loaded');
    }

    /**
     * Load player and wingman sprites
     * @returns {Promise<void>}
     */
    async loadPlayerAndWingmanSprites() {
        // Load player sprite (using crystal.png enemy sprite as requested)
        this.playerSprite = await this.loadSprite('assets/sprites/enemies/crystal.png', 'player');
        
        // Load wingman sprite (using fire.png enemy sprite as requested)
        this.wingmanSprite = await this.loadSprite('assets/sprites/enemies/fire.png', 'wingman');
        
        console.log('✅ Player and wingman sprites loaded');
    }

    /**
     * Helper method to load a single sprite
     * @param {string} path - Path to the sprite image
     * @param {string} name - Name for logging purposes
     * @returns {Promise<Image>} Loaded image
     */
    loadSprite(path, name) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                console.log(`✅ Loaded ${name} sprite: ${path}`);
                resolve(img);
            };
            
            img.onerror = () => {
                console.warn(`⚠️ Could not load ${name} sprite at ${path}`);
                resolve(null); // Resolve with null instead of rejecting to avoid breaking the game
            };
            
            img.src = path;
        });
    }

    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.isPaused = false;
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;
            this.frameCount = 0;
            this.fpsTimer = 0;
            requestAnimationFrame(this.gameLoop);
        }
    }

    /**
     * Start the actual gameplay (called when player clicks "Start Game")
     * @param {object} user - Authenticated user data
     */
    startGameplay(user = null) {
        this.gameState = 'GAME_PLAY';

        // Reset player for new game (including lives)
        if (this.playerShip) {
            this.playerShip.resetHealthAndLives();
            console.log('Player ship reset for new game');
        }

        // Start the first level
        if (this.levelManager) {
            this.levelManager.startLevel(1);
        }

        // Show power-up indicator during gameplay
        if (this.powerUpIndicator) {
            this.powerUpIndicator.show();
        }

    }

    /**
     * Start the actual gameplay (called when player clicks "Start Game")
     * @param {object} user - Authenticated user data
     */
    startGame(user = null) {

        // Start the first level
        if (this.levelManager) {
            this.levelManager.startLevel(1);
        }

        // Show power-up indicator during gameplay
        if (this.powerUpIndicator) {
            this.powerUpIndicator.show();
        }

    }

    pause() {
        if (this.gameState === 'GAME_PLAY') {
            this.gameState = 'PAUSED';
            this.isPaused = true;

            // Notify Orange SDK of pause
            if (this.orangeSDKManager) {
                this.orangeSDKManager.handleGamePause();
            }
        }
    }

    resume() {
        if (this.gameState === 'PAUSED') {
            this.gameState = 'GAME_PLAY';
            this.isPaused = false;
            // Reset timing to prevent large delta time jump
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;

            // Notify Orange SDK of resume
            if (this.orangeSDKManager) {
                this.orangeSDKManager.handleGameResume();
            }
        }
    }

    /**
     * Pause the game (for UI interfaces)
     */
    pauseGame() {
        this.pause();
    }

    /**
     * Resume the game (for UI interfaces)
     */
    resumeGame() {
        this.resume();
    }

    async destroy() {
        this.isRunning = false;

        // Handle Orange SDK quit before cleanup
        if (this.orangeSDKManager) {
            await this.orangeSDKManager.handleGameQuit();
        }

        // Cleanup input manager
        if (this.inputManager) {
            this.inputManager.destroy();
        }

        // End reality warp when game engine is destroyed
        if (this.realityWarpManager) {
            const warpState = this.realityWarpManager.getWarpState();
            if (warpState.status === 'active') {
                this.realityWarpManager.endWarp();
            }
        }

        // Cleanup bloom effect
        if (this.bloomEffect) {
            this.bloomEffect.destroy();
        }

    }

    gameLoop(currentTime) {
        if (!this.isRunning) return;

        // Calculate frame time and clamp to prevent spiral of death
        let frameTime = currentTime - this.lastFrameTime;
        if (frameTime > this.maxFrameTime) {
            frameTime = this.maxFrameTime;
        }

        this.lastFrameTime = currentTime;

        if (!this.isPaused && this.gameState === 'GAME_PLAY') {
            // Add frame time to accumulator
            this.accumulator += frameTime;

            // Fixed timestep updates - run as many updates as needed
            while (this.accumulator >= this.fixedTimeStep) {
                this.update(this.fixedTimeStep);
                this.accumulator -= this.fixedTimeStep;
            }

            // Calculate interpolation factor for smooth rendering
            const interpolation = this.accumulator / this.fixedTimeStep;

            // Always render (variable timestep for smooth visuals)
            this.render(interpolation);

            // Update FPS counter
            this.updateFPSCounter(frameTime);
        } else {
            // Still render even when paused or in menu (for menus, etc.)
            this.render(0);
        }

        requestAnimationFrame(this.gameLoop);
    }

    update(deltaTime) {
        this.updateStarfield(deltaTime);

        // Only update game logic when in GAME_PLAY state
        if (this.gameState === 'GAME_PLAY') {

            // Update player ship with movement input (handle separately from GameObjectManager)
            if (this.playerShip && this.inputManager) {
                const movementInput = this.inputManager.getMovementVector();
                this.playerShip.update(deltaTime, movementInput);

                // Update active power-ups and remove expired ones
                if (this.playerShip.activePowerUps) {
                    for (const [powerUpType, powerUp] of this.playerShip.activePowerUps) {
                        const wasActive = powerUp.isActive;
                        powerUp.update(deltaTime, this.playerShip);
                        
                        // Remove power-up from player's active map if it expired
                        if (wasActive && !powerUp.isActive) {
                            this.playerShip.activePowerUps.delete(powerUpType);
                            console.log(`Power-up ${powerUpType} expired and removed from player`);
                            
                            // Also remove from UI display
                            if (this.genieInterface && this.genieInterface.activePowerUps.has(powerUpType)) {
                                this.genieInterface.activePowerUps.delete(powerUpType);
                            }
                        }
                    }
                }
                // Handle weapon firing input
                if (this.inputManager.isActionDown('fire')) {
                    this.playerShip.fire();
                }

                // Handle consumable activation (E key)
                if (this.inputManager.isActionPressed('interact')) {
                    console.log('🔥 E key pressed - attempting consumable activation');
                    console.log('🎮 Game state:', this.gameState);
                    console.log('🎮 Consumable manager exists:', !!this.consumableManager);
                    this.activateConsumable();
                }

                // Handle consumable activation via touch (mobile)
                if (this.inputManager.isTouchDevice && this.consumableButtonBounds) {
                    this.handleConsumableTouchInput();
                }

                // Debug: Test E key detection
                if (this.inputManager.isKeyPressed('KeyE')) {
                    console.log('🔑 Raw KeyE detected!');
                }

                // Debug: Test damage system (D key)
                if (this.inputManager.isKeyPressed('KeyD')) {
                    this.playerShip.takeDamage(25); // Take 25 damage for testing
                }

                // Debug: Test healing system (H key)
                if (this.inputManager.isKeyPressed('KeyH')) {
                    this.playerShip.heal(25); // Heal 25 health for testing
                }

                // Debug: Add extra life (L key)
                if (this.inputManager.isKeyPressed('KeyL')) {
                    this.playerShip.addLives(1); // Add 1 life for testing
                }

                // Debug: Add tokens (T key)
                if (this.inputManager.isKeyPressed('KeyT')) {
                    this.tokenManager.awardTokens(50000, 'debug_tokens');
                }

                // Debug: Show Genie interface (G key)
                if (this.inputManager.isKeyPressed('KeyG')) {
                    this.showGenieInterface({ levelNumber: 1, nextLevel: 2 });
                }

                // Debug: Test power-up system (P key)
                if (this.inputManager.isKeyPressed('KeyP')) {
                    this.testPowerUpSystem();
                }
            }

            // Update active power-ups
            if (this.genieInterface) {
                this.genieInterface.updateActivePowerUps(deltaTime);

                // Update power-up indicator with current active power-ups
                if (this.powerUpIndicator) {
                    this.powerUpIndicator.updateActivePowerUps(this.genieInterface.activePowerUps);
                    this.powerUpIndicator.update(deltaTime);
                }
            }

            // Update level manager
            if (this.levelManager) {
                const gameState = {
                    playerDestroyed: this.playerShip ? this.playerShip.getHealthStatus().isDestroyed : false,
                    playerDamageTaken: false, // This would be set by damage events
                    shotsFired: 0, // This would be tracked by weapon system
                    shotsHit: 0 // This would be tracked by collision system
                };
                this.levelManager.update(deltaTime, gameState);
            }

            // Update enemy manager (only if level is in progress)
            if (this.enemyManager && this.playerShip && this.levelManager && this.levelManager.levelInProgress) {
                const playerPosition = this.playerShip.position;
                this.enemyManager.update(deltaTime, playerPosition);

                // Handle collisions
                this.handleCollisions();
            }

            // Update all other game objects (projectiles, enemies, etc.)
            if (this.gameObjectManager) {
                this.gameObjectManager.update(deltaTime);
            }

            // Clean up out-of-bounds projectiles
            this.cleanupProjectiles();

            // Update Orange SDK auto-save
            if (this.orangeSDKManager) {
                this.orangeSDKManager.updateAutoSave();
            }
            
            // Update Boss Warp Manager
            if (this.bossWarpManager) {
                this.bossWarpManager.update(deltaTime);
            }
        }

        // Update Warp VFX (purely visual) - always update for visual effects
        if (this.warpVFX) {
            this.warpVFX.update(deltaTime);
        }
    }

    /**
     * Clean up projectiles that are out of bounds
     */
    cleanupProjectiles() {
        if (!this.gameObjectManager) return;

        const bounds = {
            left: -50,
            right: this.canvas.width + 50,
            top: -50,
            bottom: this.canvas.height + 50
        };

        const projectiles = this.gameObjectManager.findByTag('projectile');
        for (const projectile of projectiles) {
            if (projectile.isOutOfBounds(bounds)) {
                this.gameObjectManager.returnToPool('projectile', projectile);
            }
        }
    }

    updateStarfield(deltaTime) {
        const dtSeconds = deltaTime / 1000;
        for (const star of this.stars) {
            star.y += star.speed * dtSeconds;
            if (star.y > this.canvas.height) {
                star.y = 0;
                star.x = Math.random() * this.canvas.width;
            }
        }
    }

    render(interpolation = 0) {
        // Clear canvas
        this.ctx.fillStyle = '#000011';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Render environment background (replaces starfield)
        let customBackgroundRendered = false;
        
        // Check for custom environment from level configuration first
        if (this.levelManager && this.levelManager.levelConfig && this.levelManager.levelConfig.environmentData) {
            const levelEnv = this.levelManager.levelConfig.environmentData;
            if (levelEnv.imageUrl) {
                const imageUrl = levelEnv.imageUrl;
                
                // Load and render custom background image
                // Only create a new Image object if the URL has changed
                if (!this.customBackgroundImage || this.customBackgroundImage.currentUrl !== imageUrl) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Attempting to load image from level config:', imageUrl);
                    this.customBackgroundImage = new Image();
                    this.customBackgroundImage.crossOrigin = 'anonymous';
                    this.customBackgroundImage.currentUrl = imageUrl; // Store the current URL
                    this.customBackgroundImage.onload = () => {
                        //console.log('✅ [GAME ENGINE] Custom background image loaded successfully from level config');
                        //console.log('🔍 [GAME ENGINE DEBUG] Image loaded successfully - dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                    };
                    this.customBackgroundImage.onerror = (error) => {
                        //console.error('❌ [GAME ENGINE] Failed to load custom background image from level config:', error);
                        //console.log('🔍 [GAME ENGINE DEBUG] Image load failed for URL:', imageUrl);
                        this.customBackgroundImage = null;
                    };
                    this.customBackgroundImage.src = imageUrl;
                }
                
                if (this.customBackgroundImage) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Image loading status:', this.customBackgroundImage.complete ? 'complete' : 'loading');
                    //console.log('🔍 [GAME ENGINE DEBUG] Image natural dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                }
                
                // Render custom background if image is loaded or if we have a cached image
                if (this.customBackgroundImage && this.customBackgroundImage.complete) {
                    // Render custom background
                    this.ctx.save();
                    
                    // Calculate scaling to cover entire canvas while maintaining aspect ratio
                    const imageAspect = this.customBackgroundImage.naturalWidth / this.customBackgroundImage.naturalHeight;
                    const canvasAspect = this.canvas.width / this.canvas.height;
                    
                    let drawWidth, drawHeight, drawX, drawY;
                    
                    if (imageAspect > canvasAspect) {
                        // Image is wider than canvas
                        drawHeight = this.canvas.height;
                        drawWidth = drawHeight * imageAspect;
                        drawX = (this.canvas.width - drawWidth) / 2;
                        drawY = 0;
                    } else {
                        // Image is taller than canvas
                        drawWidth = this.canvas.width;
                        drawHeight = drawWidth / imageAspect;
                        drawX = 0;
                        drawY = (this.canvas.height - drawHeight) / 2;
                    }
                    
                    this.ctx.drawImage(this.customBackgroundImage, drawX, drawY, drawWidth, drawHeight);
                    this.ctx.restore();
                    
                    customBackgroundRendered = true;
                } else if (this.customBackgroundImage && !this.customBackgroundImage.complete) {
                    // Image is still loading, but we want to show the custom background
                    // We'll show a placeholder or just not render the starfield
                    customBackgroundRendered = true;
                }
            }
        }
        
        // Fallback to RealityWarpManager environment if no level config environment
        if (!customBackgroundRendered && this.realityWarpManager && this.realityWarpManager.currentEnvironment &&
            this.realityWarpManager.getWarpState().status === 'active') {
            //console.log('🔍 [GAME ENGINE DEBUG] Reality warp is active, checking for custom environment');
            //console.log('🔍 [GAME ENGINE DEBUG] Warp state:', this.realityWarpManager.getWarpState());
            //console.log('🔍 [GAME ENGINE DEBUG] Current environment:', this.realityWarpManager.currentEnvironment);
            const customEnv = this.realityWarpManager.currentEnvironment;
            if (customEnv.imageData && customEnv.imageData.images && customEnv.imageData.images[0]) {
                const imageData = customEnv.imageData.images[0];
                const imageUrl = imageData.localUrl || imageData.url;
                
                // Load and render custom background image
                // Only create a new Image object if the URL has changed
                if (!this.customBackgroundImage || this.customBackgroundImage.currentUrl !== imageUrl) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Attempting to load image from RealityWarpManager:', imageUrl);
                    this.customBackgroundImage = new Image();
                    this.customBackgroundImage.crossOrigin = 'anonymous';
                    this.customBackgroundImage.currentUrl = imageUrl; // Store the current URL
                    this.customBackgroundImage.onload = () => {
                        //console.log('✅ [GAME ENGINE] Custom background image loaded successfully from RealityWarpManager');
                        //console.log('🔍 [GAME ENGINE DEBUG] Image loaded successfully - dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                    };
                    this.customBackgroundImage.onerror = (error) => {
                        //console.error('❌ [GAME ENGINE] Failed to load custom background image from RealityWarpManager:', error);
                        //console.log('🔍 [GAME ENGINE DEBUG] Image load failed for URL:', imageUrl);
                        this.customBackgroundImage = null;
                    };
                    this.customBackgroundImage.src = imageUrl;
                }
                
                if (this.customBackgroundImage) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Image loading status:', this.customBackgroundImage.complete ? 'complete' : 'loading');
                    //console.log('🔍 [GAME ENGINE DEBUG] Image natural dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                }
                
                // Render custom background if image is loaded or if we have a cached image
                if (this.customBackgroundImage && this.customBackgroundImage.complete) {
                    // Render custom background
                    this.ctx.save();
                    
                    // Calculate scaling to cover entire canvas while maintaining aspect ratio
                    const imageAspect = this.customBackgroundImage.naturalWidth / this.customBackgroundImage.naturalHeight;
                    const canvasAspect = this.canvas.width / this.canvas.height;
                    
                    let drawWidth, drawHeight, drawX, drawY;
                    
                    if (imageAspect > canvasAspect) {
                        // Image is wider than canvas
                        drawHeight = this.canvas.height;
                        drawWidth = drawHeight * imageAspect;
                        drawX = (this.canvas.width - drawWidth) / 2;
                        drawY = 0;
                    } else {
                        // Image is taller than canvas
                        drawWidth = this.canvas.width;
                        drawHeight = drawWidth / imageAspect;
                        drawX = 0;
                        drawY = (this.canvas.height - drawHeight) / 2;
                    }
                    
                    this.ctx.drawImage(this.customBackgroundImage, drawX, drawY, drawWidth, drawHeight);
                    this.ctx.restore();
                    
                    customBackgroundRendered = true;
                } else if (this.customBackgroundImage && !this.customBackgroundImage.complete) {
                    // Image is still loading, but we want to show the custom background
                    // We'll show a placeholder or just not render the starfield
                    customBackgroundRendered = true;
                }
            }
        }
        
        // If no custom background, use AI service manager or fallback to default background
        if (!customBackgroundRendered) {
            if (this.aiServiceManager) {
                this.aiServiceManager.render(this.ctx, this.canvas.width, this.canvas.height);
            } else {
                // Fallback to default background image if available
                this.renderDefaultBackground();
            }
        }

        // Apply bloom effect to game objects only (not background)
        if (this.bloomEffect) {
            // Create temporary canvas for game objects
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height;
            const tempCtx = tempCanvas.getContext('2d');

            // Render game objects to temporary canvas
            if (this.playerShip) {
                this.playerShip.render(tempCtx);
            }

            if (this.enemyManager) {
                this.enemyManager.render(tempCtx, interpolation);
            }

            if (this.gameObjectManager) {
                this.gameObjectManager.render(tempCtx, interpolation);
            }

            // Apply bloom to the temporary canvas
            const originalCanvas = this.bloomEffect.canvas;
            const originalCtx = this.bloomEffect.ctx;
            this.bloomEffect.canvas = tempCanvas;
            this.bloomEffect.ctx = tempCtx;
            this.bloomEffect.applyBloom();
            this.bloomEffect.canvas = originalCanvas;
            this.bloomEffect.ctx = originalCtx;

            // Draw the bloomed game objects onto the main canvas (over the background)
            this.ctx.drawImage(tempCanvas, 0, 0);
        } else {
            // No bloom - render game objects directly
            if (this.playerShip) {
                this.playerShip.render(this.ctx);
            }

            if (this.enemyManager) {
                this.enemyManager.render(this.ctx, interpolation);
            }

            if (this.gameObjectManager) {
                this.gameObjectManager.render(this.ctx, interpolation);
            }
        }

        // Game title (smaller and positioned at top)
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '20px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Chaos Cruiser', this.canvas.width / 2, 30);

        // Display input debug info
        if (this.inputManager) {
            this.renderInputDebug();

            // Render input manager (virtual joystick, etc.)
            this.inputManager.render(this.ctx);
        }

        // FPS counter removed - not needed for gameplay

        // Display health and lives UI
        this.renderHealthAndLivesUI();

        // Display level and score UI
        this.renderLevelAndScoreUI();

        // Display token balance and reward animations
        if (this.tokenManager) {
            this.tokenManager.render(this.ctx, this.fixedTimeStep);
        }

        // Render power-up indicators
        if (this.powerUpIndicator && this.gameState === 'GAME_PLAY') {
            this.powerUpIndicator.render(this.ctx);
        }

        // Render consumable HUD
        if (this.gameState === 'GAME_PLAY') {
            this.renderConsumableHUD();
        }

        // Render Warp VFX overlays last (on top of everything)
        if (this.warpVFX) {
            this.warpVFX.render(this.ctx);
        }
    }

    /**
     * Change the current game environment
     * @param {string} environmentDescription - Description of the new environment
     */
    async changeEnvironment(environmentDescription) {
        if (this.aiServiceManager) {
            try {
                await this.aiServiceManager.createEnvironment(environmentDescription);
                console.log(`Environment changed to: ${environmentDescription}`);
            } catch (error) {
                console.error('Failed to change environment:', error);
            }
        }
    }

    /**
     * Get the current environment modifiers
     * @returns {Object} Current environment modifiers
     */
    getCurrentEnvironmentModifiers() {
        if (this.aiServiceManager) {
            return this.aiServiceManager.getCurrentEnvironmentModifiers();
        }
        return {};
    }

    /**
     * Apply environment modifiers to enemy stats
     * @param {Object} baseStats - Base enemy stats
     * @returns {Object} Modified stats with environment effects
     */
    applyEnvironmentModifiers(baseStats) {
        if (this.aiServiceManager) {
            return this.aiServiceManager.applyEnvironmentModifiers(baseStats);
        }
        return baseStats;
    }

    renderInputDebug() {
        // Input debug rendering removed - not needed for gameplay
        // This method is kept as empty function to maintain function calls
    }

    updateFPSCounter(frameTime) {
        this.frameCount++;
        this.fpsTimer += frameTime;

        // Update FPS display every second
        if (this.fpsTimer >= 1000) {
            this.currentFPS = Math.round((this.frameCount * 1000) / this.fpsTimer);
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }

    renderDefaultBackground() {
        // Use default.jpg as the default background
        if (!this.defaultBackgroundImage) {
            // Create and load the default background image
            this.defaultBackgroundImage = new Image();
            this.defaultBackgroundImage.crossOrigin = 'anonymous';
            this.defaultBackgroundImage.onload = () => {
                // Image loaded successfully
            };
            this.defaultBackgroundImage.onerror = () => {
                console.error('Failed to load default background image');
                this.defaultBackgroundImage = null;
                // Fallback to starfield if default image fails to load
                this.renderStarField();
                return;
            };
            this.defaultBackgroundImage.src = '../server/images/default.jpg';
        }

        if (this.defaultBackgroundImage && this.defaultBackgroundImage.complete) {
            // Render default background
            this.ctx.save();
            
            // Calculate scaling to cover entire canvas while maintaining aspect ratio
            const imageAspect = this.defaultBackgroundImage.naturalWidth / this.defaultBackgroundImage.naturalHeight;
            const canvasAspect = this.canvas.width / this.canvas.height;
            
            let drawWidth, drawHeight, drawX, drawY;
            
            if (imageAspect > canvasAspect) {
                // Image is wider than canvas
                drawHeight = this.canvas.height;
                drawWidth = drawHeight * imageAspect;
                drawX = (this.canvas.width - drawWidth) / 2;
                drawY = 0;
            } else {
                // Image is taller than canvas
                drawWidth = this.canvas.width;
                drawHeight = drawWidth / imageAspect;
                drawX = 0;
                drawY = (this.canvas.height - drawHeight) / 2;
            }
            
            this.ctx.drawImage(this.defaultBackgroundImage, drawX, drawY, drawWidth, drawHeight);
            this.ctx.restore();
        } else {
            // Image is still loading or failed, fallback to starfield
            this.renderStarField();
        }
    }

    renderStarField() {
        // Simple star field effect
        this.ctx.fillStyle = '#ffffff';
        for (const star of this.stars) {
            this.ctx.fillRect(star.x, star.y, star.size, star.size);
        }
    }

    /**
     * Render health bar and lives counter UI
     */
    renderHealthAndLivesUI() {
        if (!this.playerShip) return;

        const healthStatus = this.playerShip.getHealthStatus();

        // Health bar position (top-right corner)
        const healthBarX = this.canvas.width - 240;
        const healthBarY = 20;
        const healthBarWidth = 220;
        const healthBarHeight = 30;

        // Create sci-fi background panel
        this.ctx.fillStyle = 'rgba(10, 10, 46, 0.8)';
        this.ctx.fillRect(healthBarX - 10, healthBarY - 10, healthBarWidth + 20, healthBarHeight + 60);
        
        // Panel border with glow effect
        this.ctx.strokeStyle = '#00ffff';
        this.ctx.lineWidth = 2;
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 15;
        this.ctx.strokeRect(healthBarX - 10, healthBarY - 10, healthBarWidth + 20, healthBarHeight + 60);
        this.ctx.shadowBlur = 0;

        // Health bar background with inner glow
        const gradient = this.ctx.createLinearGradient(healthBarX, healthBarY, healthBarX + healthBarWidth, healthBarY);
        gradient.addColorStop(0, 'rgba(0, 20, 40, 0.9)');
        gradient.addColorStop(1, 'rgba(0, 10, 30, 0.9)');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);

        // Health bar border
        this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.5)';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);

        // Health bar fill with gradient and animation
        const healthWidth = (healthBarWidth - 4) * healthStatus.healthPercentage;
        let healthGradient = this.ctx.createLinearGradient(healthBarX + 2, healthBarY, healthBarX + healthWidth, healthBarY);
        
        if (healthStatus.healthPercentage < 0.3) {
            // Critical health - red with pulsing effect
            const pulseIntensity = 0.7 + 0.3 * Math.sin(Date.now() / 200);
            healthGradient.addColorStop(0, `rgba(255, 0, 0, ${pulseIntensity})`);
            healthGradient.addColorStop(0.5, `rgba(255, 100, 0, ${pulseIntensity})`);
            healthGradient.addColorStop(1, `rgba(255, 0, 0, ${pulseIntensity})`);
        } else if (healthStatus.healthPercentage < 0.6) {
            // Damaged health - orange
            healthGradient.addColorStop(0, '#ff6600');
            healthGradient.addColorStop(0.5, '#ff9900');
            healthGradient.addColorStop(1, '#ff6600');
        } else {
            // Healthy - green with sci-fi blue tint
            healthGradient.addColorStop(0, '#00ff88');
            healthGradient.addColorStop(0.5, '#0b00a0ff');
            healthGradient.addColorStop(1, '#00ff88');
        }

        this.ctx.fillStyle = healthGradient;
        this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);

        // Add health bar glow effect
        if (healthStatus.healthPercentage > 0.1) {
            this.ctx.shadowColor = healthStatus.healthPercentage < 0.3 ? '#ff0000' :
                                   healthStatus.healthPercentage < 0.6 ? '#ff9900' : '#00ffff';
            this.ctx.shadowBlur = 10;
            this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);
            this.ctx.shadowBlur = 0;
        }

        // Health text with sci-fi styling
        this.ctx.fillStyle = '#b90d07ff';
        this.ctx.font = 'bold 14px "Courier New", monospace';
        this.ctx.textAlign = 'center';
        this.ctx.shadowColor = '#ce6006ff';
        this.ctx.shadowBlur = 5;
        this.ctx.fillText(
            `HULL INTEGRITY: ${healthStatus.health}/${healthStatus.maxHealth}`,
            healthBarX + healthBarWidth / 2,
            healthBarY + healthBarHeight / 2 + 5
        );
        this.ctx.shadowBlur = 0;

        // Lives counter with sci-fi styling
        const livesY = healthBarY + healthBarHeight + 25;
        this.ctx.textAlign = 'right';
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 16px "Courier New", monospace';
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 5;
        this.ctx.fillText(`LIVES: ${healthStatus.lives}`, this.canvas.width - 20, livesY);
        this.ctx.shadowBlur = 0;

        // Draw sci-fi life icons
        const lifeIconSize = 18;
        const lifeIconSpacing = 22;
        const lifeIconStartX = this.canvas.width - 20 - (healthStatus.lives * lifeIconSpacing);

        for (let i = 0; i < healthStatus.lives; i++) {
            const iconX = lifeIconStartX + (i * lifeIconSpacing);
            const iconY = livesY + 12;

            // Draw hexagonal life icon
            this.ctx.save();
            this.ctx.translate(iconX, iconY);
            this.ctx.beginPath();
            for (let j = 0; j < 6; j++) {
                const angle = (j * Math.PI) / 3;
                const x = Math.cos(angle) * (lifeIconSize / 2);
                const y = Math.sin(angle) * (lifeIconSize / 2);
                if (j === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }
            this.ctx.closePath();
            
            // Fill with gradient
            const iconGradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, lifeIconSize / 2);
            iconGradient.addColorStop(0, '#00ffff');
            iconGradient.addColorStop(1, '#0088ff');
            this.ctx.fillStyle = iconGradient;
            this.ctx.fill();
            
            // Add glow
            this.ctx.strokeStyle = '#00ffff';
            this.ctx.lineWidth = 1;
            this.ctx.shadowColor = '#00ffff';
            this.ctx.shadowBlur = 8;
            this.ctx.stroke();
            this.ctx.shadowBlur = 0;
            this.ctx.restore();
        }

        // Show invulnerability status with sci-fi styling
        if (healthStatus.isInvulnerable) {
            this.ctx.fillStyle = '#ffff00';
            this.ctx.font = 'bold 12px "Courier New", monospace';
            this.ctx.textAlign = 'right';
            this.ctx.shadowColor = '#ffff00';
            this.ctx.shadowBlur = 8;
            const invulnTime = (healthStatus.invulnerabilityTimeRemaining / 1000).toFixed(1);
            this.ctx.fillText(`QUANTUM SHIELD: ${invulnTime}s`, this.canvas.width - 20, livesY + 50);
            this.ctx.shadowBlur = 0;
        }

        // Show game over message with sci-fi styling
        if (healthStatus.isDestroyed) {
            // Dark overlay with sci-fi gradient
            const overlayGradient = this.ctx.createRadialGradient(
                this.canvas.width / 2, this.canvas.height / 2, 0,
                this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
            );
            overlayGradient.addColorStop(0, 'rgba(255, 0, 0, 0.2)');
            overlayGradient.addColorStop(1, 'rgba(0, 0, 0, 0.9)');
            this.ctx.fillStyle = overlayGradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            // Game over text with pulsing effect
            const pulseScale = 1 + 0.1 * Math.sin(Date.now() / 300);
            this.ctx.save();
            this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2 - 50);
            this.ctx.scale(pulseScale, pulseScale);
            this.ctx.fillStyle = '#ff4444';
            this.ctx.font = 'bold 48px "Courier New", monospace';
            this.ctx.textAlign = 'center';
            this.ctx.shadowColor = '#ff0000';
            this.ctx.shadowBlur = 20;
            this.ctx.fillText('SYSTEM FAILURE', 0, 0);
            this.ctx.restore();

            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = 'bold 24px "Courier New", monospace';
            this.ctx.textAlign = 'center';
            this.ctx.shadowColor = '#ffffff';
            this.ctx.shadowBlur = 10;
            this.ctx.fillText('HULL INTEGRITY: 0%', this.canvas.width / 2, this.canvas.height / 2 + 50);
            this.ctx.shadowBlur = 0;
        }
    }

    /**
     * Render level and score UI
     */
    renderLevelAndScoreUI() {
        if (!this.levelManager) return;

        const levelStatus = this.levelManager.getLevelStatus();

        // Create sci-fi background panel for stats (half the size)
        const panelX = 10;
        const panelY = 15; // Moved higher to align with health/shield indicator
        const panelWidth = 125; // Half the original width
        const panelHeight = 110; // Half the original height

        // Panel background with gradient
        const panelGradient = this.ctx.createLinearGradient(panelX, panelY, panelX + panelWidth, panelY + panelHeight);
        panelGradient.addColorStop(0, 'rgba(10, 10, 46, 0.8)');
        panelGradient.addColorStop(1, 'rgba(0, 10, 30, 0.8)');
        this.ctx.fillStyle = panelGradient;
        this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);

        // Panel border with glow
        this.ctx.strokeStyle = '#00ffff';
        this.ctx.lineWidth = 2;
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 15;
        this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
        this.ctx.shadowBlur = 0;

        // Level information with sci-fi styling (smaller font)
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 12px "Courier New", monospace';
        this.ctx.textAlign = 'left';
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 5;
        this.ctx.fillText(`SECTOR: ${levelStatus.currentLevel}`, panelX + 10, panelY + 20);

        // Score display with sci-fi styling (smaller font)
        this.ctx.font = 'bold 10px "Courier New", monospace';
        this.ctx.fillText(`SCORE: ${levelStatus.score.current.toLocaleString()}`, panelX + 10, panelY + 35);
        this.ctx.fillText(`SECTOR: ${levelStatus.score.level.toLocaleString()}`, panelX + 10, panelY + 50);
        this.ctx.shadowBlur = 0;

        // Progress display with sci-fi styling (smaller font)
        if (levelStatus.levelInProgress) {
            // Get wave-specific enemy data from EnemyManager
            let waveEnemiesKilled = 0;
            let waveTotalEnemies = 0;
            
            if (this.enemyManager && this.enemyManager.waveInProgress) {
                const waveStatus = this.enemyManager.getWaveStatus();
                waveEnemiesKilled = waveStatus.enemiesKilled;
                waveTotalEnemies = waveStatus.totalEnemies;
            }
            
            // Enemies defeated in current wave
            const progressText = `TARGETS: ${waveEnemiesKilled}/${waveTotalEnemies}`;
            this.ctx.fillStyle = '#00ffff';
            this.ctx.font = 'bold 10px "Courier New", monospace';
            this.ctx.fillText(progressText, panelX + 10, panelY + 65);

            // Waves completed
            const waveText = `WAVES: ${levelStatus.progress.wavesCompleted}/${levelStatus.progress.requiredWaves}`;
            this.ctx.fillText(waveText, panelX + 10, panelY + 80);

            // Progress bar for waves (smaller)
            const progressBarX = panelX + 10;
            const progressBarY = panelY + 85;
            const progressBarWidth = panelWidth - 20;
            const progressBarHeight = 6;
            
            // Progress bar background
            this.ctx.fillStyle = 'rgba(0, 20, 40, 0.8)';
            this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);
            
            // Progress bar border
            this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.5)';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);
            
            // Progress bar fill
            const progressPercentage = levelStatus.progress.wavesCompleted / levelStatus.progress.requiredWaves;
            const progressFillWidth = progressBarWidth * progressPercentage;
            const progressGradient = this.ctx.createLinearGradient(progressBarX, progressBarY, progressBarX + progressFillWidth, progressBarY);
            progressGradient.addColorStop(0, '#00ffff');
            progressGradient.addColorStop(1, '#0088ff');
            this.ctx.fillStyle = progressGradient;
            this.ctx.fillRect(progressBarX, progressBarY, progressFillWidth, progressBarHeight);
        }

        // Level completion overlay
        if (!levelStatus.levelInProgress && levelStatus.levelConfig) {
            this.renderLevelCompletionOverlay(levelStatus);
        }
    }

    /**
     * Render level completion overlay
     * @param {object} levelStatus - Current level status
     */
    renderLevelCompletionOverlay(levelStatus) {
        // Sci-fi semi-transparent overlay with gradient
        const overlayGradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
        );
        overlayGradient.addColorStop(0, 'rgba(0, 20, 40, 0.9)');
        overlayGradient.addColorStop(1, 'rgba(0, 0, 20, 0.95)');
        this.ctx.fillStyle = overlayGradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Sci-fi level complete text with glow
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 42px "Courier New", monospace';
        this.ctx.textAlign = 'center';
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 20;
        this.ctx.fillText(`SECTOR ${levelStatus.currentLevel} COMPLETE`, this.canvas.width / 2, this.canvas.height / 2 - 70);

        // Decorative hexagon around text
        this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)';
        this.ctx.lineWidth = 2;
        this.ctx.shadowBlur = 10;
        const hexSize = 180;
        const hexX = this.canvas.width / 2;
        const hexY = this.canvas.height / 2 - 70;
        this.ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (Math.PI / 3) * i;
            const x = hexX + hexSize * Math.cos(angle);
            const y = hexY + hexSize * Math.sin(angle);
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        this.ctx.closePath();
        this.ctx.stroke();
        this.ctx.shadowBlur = 0;

        // Score breakdown with sci-fi styling
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 22px "Courier New", monospace';
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 10;
        this.ctx.fillText(`FINAL SCORE: ${levelStatus.score.level.toLocaleString()}`, this.canvas.width / 2, this.canvas.height / 2);

        this.ctx.font = 'bold 16px "Courier New", monospace';
        this.ctx.fillText(`TARGETS ELIMINATED: ${levelStatus.progress.enemiesDefeated}`, this.canvas.width / 2, this.canvas.height / 2 + 35);

        const timeText = `COMPLETION TIME: ${(levelStatus.completionTime / 1000).toFixed(2)}s`;
        this.ctx.fillText(timeText, this.canvas.width / 2, this.canvas.height / 2 + 60);

        // WISH token reward display if available
        if (levelStatus.score && levelStatus.score.tokenReward > 0) {
            this.ctx.fillStyle = '#ff00ff';
            this.ctx.shadowColor = '#ff00ff';
            this.ctx.shadowBlur = 15;
            this.ctx.font = 'bold 18px "Courier New", monospace';
            this.ctx.fillText(`WISH TOKENS EARNED: ${levelStatus.score.tokenReward}`, this.canvas.width / 2, this.canvas.height / 2 + 90);
        }

        // Next level indicator with sci-fi styling
        this.ctx.fillStyle = '#00ffff';
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 8;
        this.ctx.font = 'bold 14px "Courier New", monospace';
        
        // Blinking text effect
        const blinkAlpha = 0.5 + 0.5 * Math.sin(Date.now() / 300);
        this.ctx.globalAlpha = blinkAlpha;
        this.ctx.fillText('INITIALIZING NEXT SECTOR...', this.canvas.width / 2, this.canvas.height / 2 + 130);
        this.ctx.globalAlpha = 1.0;
        this.ctx.shadowBlur = 0;
    }

    /**
     * Render consumable HUD display
     */
    renderConsumableHUD() {
        if (!this.consumableManager) return;

        const currentConsumable = this.consumableManager.getCurrentConsumable();
        if (!currentConsumable) return;

        const displayInfo = currentConsumable.getDisplayInfo();

        // Position the consumable HUD in the bottom-left corner
        const hudX = 20;
        const hudY = this.canvas.height - 80;
        const hudWidth = 200;
        const hudHeight = 60;

        // Background panel with sci-fi styling
        const panelGradient = this.ctx.createLinearGradient(hudX, hudY, hudX + hudWidth, hudY + hudHeight);
        panelGradient.addColorStop(0, 'rgba(10, 10, 46, 0.9)');
        panelGradient.addColorStop(1, 'rgba(0, 10, 30, 0.9)');
        this.ctx.fillStyle = panelGradient;
        this.ctx.fillRect(hudX, hudY, hudWidth, hudHeight);

        // Panel border with glow
        this.ctx.strokeStyle = displayInfo.color;
        this.ctx.lineWidth = 2;
        this.ctx.shadowColor = displayInfo.color;
        this.ctx.shadowBlur = 10;
        this.ctx.strokeRect(hudX, hudY, hudWidth, hudHeight);
        this.ctx.shadowBlur = 0;

        // Consumable icon
        this.ctx.fillStyle = displayInfo.color;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(displayInfo.icon, hudX + 10, hudY + 30);

        // Consumable name
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 12px "Courier New", monospace';
        this.ctx.fillText(displayInfo.name, hudX + 45, hudY + 20);

        // Activation prompt
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 10px "Courier New", monospace';
        this.ctx.fillText('Press [E] to activate', hudX + 45, hudY + 35);

        // Usage indicator
        this.ctx.fillStyle = '#ffaa00';
        this.ctx.font = '9px "Courier New", monospace';
        this.ctx.fillText('Single-use tactical item', hudX + 45, hudY + 50);

        // Add touch button for mobile devices
        if (this.inputManager && this.inputManager.isTouchDevice) {
            this.renderConsumableTouchButton(currentConsumable);
        }
    }

    /**
     * Render touch button for consumable activation on mobile devices
     * @param {Consumable} consumable - The current consumable
     */
    renderConsumableTouchButton(consumable) {
        const displayInfo = consumable.getDisplayInfo();

        // Position button in bottom-right corner
        const buttonSize = 60;
        const buttonX = this.canvas.width - buttonSize - 20;
        const buttonY = this.canvas.height - buttonSize - 20;

        // Store button bounds for touch detection
        this.consumableButtonBounds = {
            x: buttonX,
            y: buttonY,
            width: buttonSize,
            height: buttonSize
        };

        // Button background with glow effect
        const buttonGradient = this.ctx.createRadialGradient(
            buttonX + buttonSize/2, buttonY + buttonSize/2, 0,
            buttonX + buttonSize/2, buttonY + buttonSize/2, buttonSize/2
        );
        buttonGradient.addColorStop(0, displayInfo.color + '80');
        buttonGradient.addColorStop(1, displayInfo.color + '40');
        this.ctx.fillStyle = buttonGradient;
        this.ctx.fillRect(buttonX, buttonY, buttonSize, buttonSize);

        // Button border
        this.ctx.strokeStyle = displayInfo.color;
        this.ctx.lineWidth = 3;
        this.ctx.shadowColor = displayInfo.color;
        this.ctx.shadowBlur = 15;
        this.ctx.strokeRect(buttonX, buttonY, buttonSize, buttonSize);
        this.ctx.shadowBlur = 0;

        // Consumable icon
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 32px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(displayInfo.icon, buttonX + buttonSize/2, buttonY + buttonSize/2 + 10);

        // 'E' key indicator
        this.ctx.fillStyle = '#00ffff';
        this.ctx.font = 'bold 12px "Courier New", monospace';
        this.ctx.fillText('E', buttonX + buttonSize/2, buttonY + buttonSize - 5);
    }

    /**
     * Set up level manager callbacks
     */
    setupLevelManagerCallbacks() {
        // Level start callback
        this.levelManager.setOnLevelStart((levelNumber, levelConfig) => {
            console.log(`Level ${levelNumber} started:`, levelConfig);

            // Reset player ship health for new level (lives are preserved across levels)
            if (this.playerShip) {
                this.playerShip.resetHealthOnly();
                console.log('Player ship health reset for new level, lives preserved');
            }

            // Reset enemy manager for new level
            if (this.enemyManager) {
                this.enemyManager.reset();
                console.log('Enemy manager reset for new level');
            }
        });

        // Level completion callback
        this.levelManager.setOnLevelComplete((completionData) => {
            console.log('Level completed:', completionData);

            if (completionData.completed) {
                // Level completed successfully
                console.log(`Level ${completionData.levelNumber} completed in ${completionData.completionTime.toFixed(2)}s`);
                console.log(`Score: ${completionData.score.totalScore}, Enemies: ${completionData.enemiesDefeated}`);

                // Calculate and award WISH tokens
                if (this.tokenManager) {
                    const tokenReward = this.tokenManager.calculateLevelReward(completionData);
                    if (tokenReward.totalReward > 0) {
                        const awardResult = this.tokenManager.awardTokens(
                            tokenReward.totalReward,
                            'level_completion',
                            {
                                levelNumber: completionData.levelNumber,
                                completionTime: completionData.completionTime,
                                score: completionData.score.totalScore,
                                bonuses: completionData.bonuses,
                                breakdown: tokenReward.breakdown
                            }
                        );

                        console.log(`Awarded ${tokenReward.totalReward} WISH tokens for level completion`);
                        console.log('Token reward breakdown:', tokenReward.breakdown);

                        // Update completion data with token reward for Orange SDK
                        completionData.score.tokenReward = tokenReward.totalReward;
                    }
                }

                // Save progress to Orange SDK
                if (this.orangeSDKManager) {
                    this.orangeSDKManager.onLevelCompleted(completionData);
                }

                // Show Genie interface for power-up purchases
                this.showGenieInterface(completionData);
            } else {
                // Level failed
                console.log(`Level ${completionData.levelNumber} failed: ${completionData.reason}`);

                // Auto-retry after a brief delay
                setTimeout(() => {
                    if (completionData.canRetry) {
                        this.levelManager.startLevel(completionData.levelNumber);
                    }
                }, 3000);
            }
        });

        // Score update callback
        this.levelManager.setOnScoreUpdate((scoreData) => {
            // Update UI with new score information
            // This could trigger score display animations, etc.
        });
    }

    /**
     * Set up enemy manager callbacks
     */
    setupEnemyManagerCallbacks() {
        // Wave completion callback
        this.enemyManager.onWaveComplete = (waveNumber, bonus) => {
            console.log(`Wave ${waveNumber} completed with bonus: ${bonus}`);

            // Record wave completion in level manager
            if (this.levelManager) {
                this.levelManager.recordWaveCompletion(waveNumber, bonus);
            }
        };

        // Enemy escaped callback
        this.enemyManager.onEnemyEscaped = (enemy) => {
            console.log(`Enemy ${enemy.id} escaped off-screen`);

            // Record enemy escape in level manager (treat as defeated for progress)
            if (this.levelManager) {
                this.levelManager.recordEnemyDefeat(enemy, 0); // 0 score for escaped enemies
            }
        };
    }

    /**
     * Set up boss warp manager callbacks
     */
    setupBossWarpManagerCallbacks() {
        if (!this.bossWarpManager) return;

        // Set up callbacks for boss warp events
        this.bossWarpManager.onBossWarpStart = (warpData) => {
            console.log('Boss warp started:', warpData);
            // Handle boss warp start
        };

        this.bossWarpManager.onBossWarpComplete = (warpData) => {
            console.log('Boss warp completed:', warpData);
            // Handle boss warp completion
        };

        this.bossWarpManager.onBossWarpFailed = (warpData) => {
            console.log('Boss warp failed:', warpData);
            // Handle boss warp failure
        };
    }

    /**
     * Handle collisions between game objects
     */
    handleCollisions() {
        if (!this.enemyManager || !this.playerShip) return;
        
        // Debug: confirm handleCollisions is being called
        console.log('handleCollisions() called');

        // Check player-enemy collisions
        const playerCollisions = this.enemyManager.checkPlayerCollisions(this.playerShip);
        for (const enemy of playerCollisions) {
            const result = this.enemyManager.handlePlayerEnemyCollision(this.playerShip, enemy);
            console.log('Player-Enemy collision:', result);
        }

        // Check projectile-enemy collisions
        const projectiles = this.gameObjectManager.findByTag('projectile');
        const projectileCollisions = this.enemyManager.checkProjectileCollisions(projectiles);

        for (const collision of projectileCollisions) {
            const result = this.enemyManager.handleProjectileEnemyCollision(
                collision.projectile,
                collision.enemy,
                collision.damage
            );

            // Record enemy defeat in level manager
            if (result.enemyDestroyed && this.levelManager) {
                this.levelManager.recordEnemyDefeat(collision.enemy, result.scoreGained);
                
                // Spawn power-up collectible if dropped
                if (result.powerUpDropType) {
                    this.spawnPowerUpCollectible(result.powerUpDropType, result.enemyPosition);
                }
            }
        }

        // Check enemy projectile-player collisions
        if (this.enemyManager && this.enemyManager.projectileSystem && this.playerShip && this.playerShip.active) {
            const enemyProjectileCollisions = this.enemyManager.projectileSystem.checkPlayerCollisions(this.playerShip);
            for (const projectile of enemyProjectileCollisions) {
                const result = this.enemyManager.projectileSystem.handlePlayerCollision(projectile, this.playerShip);
                console.log('Enemy projectile hit player:', result);
                
                // Apply damage to player
                if (result.damage) {
                    this.playerShip.takeDamage(result.damage);
                }
            }
        }

        // Check reflected projectile-enemy collisions
        const reflectedProjectiles = this.gameObjectManager.findByTag('player-projectile');
        if (reflectedProjectiles.length > 0 && this.enemyManager) {
            const reflectedCollisions = this.enemyManager.checkProjectileCollisions(reflectedProjectiles);
            for (const collision of reflectedCollisions) {
                const result = this.enemyManager.handleProjectileEnemyCollision(
                    collision.projectile,
                    collision.enemy,
                    collision.damage
                );

                // Record enemy defeat in level manager
                if (result.enemyDestroyed && this.levelManager) {
                    this.levelManager.recordEnemyDefeat(collision.enemy, result.scoreGained);
                    
                    // Spawn power-up collectible if dropped
                    if (result.powerUpDropType) {
                        this.spawnPowerUpCollectible(result.powerUpDropType, result.enemyPosition);
                    }
                }
                
                // Destroy reflected projectile after hitting enemy
                collision.projectile.destroy();
            }
        }

        // Check player-powerup collectible collisions (custom check since player is not in GameObjectManager)
        const powerupCollectibles = this.gameObjectManager.findByTag('powerup-collectible');
        if (this.playerShip && this.playerShip.active) {
            for (const collectible of powerupCollectibles) {
                if (collectible.active && this.playerShip.collidesWith(collectible)) {
                    console.log('Player collided with power-up collectible:', collectible.powerUpType);
                    this.handlePowerUpCollection(collectible);
                }
            }
        }
    }

    /**
     * Spawn a power-up collectible in the game world
     * @param {string} powerUpType - Type of power-up to spawn
     * @param {Vector2} position - Position where to spawn the collectible
     */
    async spawnPowerUpCollectible(powerUpType, position) {
        try {
            // Check if there are already 2 power-ups on the field
            const existingPowerUps = this.gameObjectManager.findByTag('powerup-collectible');
            const activePowerUps = existingPowerUps.filter(powerUp => powerUp.active && !powerUp.collected);
            
            if (activePowerUps.length >= 2) {
                console.log(`Cannot spawn ${powerUpType} power-up - already ${activePowerUps.length} power-ups on the field (limit: 2)`);
                return;
            }

            // Create power-up collectible
            const { PowerUpCollectible } = await import('../entities/PowerUpCollectible.js');
            const collectible = new PowerUpCollectible(position.x, position.y, powerUpType);

            // Add to game object manager
            this.gameObjectManager.add(collectible);

            console.log(`Spawned ${powerUpType} power-up collectible at position: ${position.x}, ${position.y}`);
        } catch (error) {
            console.error('Error spawning power-up collectible:', error);
        }
    }

    /**
     * Handle power-up collection when player collides with collectible
     * @param {PowerUpCollectible} collectible - The collectible that was collected
     */
    async handlePowerUpCollection(collectible) {
        try {
            const powerUp = collectible.collect();
            if (!powerUp) {
                console.log('Power-up collection failed - no power-up returned from collectible');
                return;
            }
            
            // Apply the power-up to the player
            const success = await powerUp.apply(this.playerShip);
            
            if (success) {
                console.log(`Power-up ${powerUp.type} collected and applied successfully`);
                
                // Add to player's active power-ups map
                this.playerShip.activePowerUps.set(powerUp.type, powerUp);
                
                // Add to active power-ups for UI display
                if (this.genieInterface) {
                    this.genieInterface.activePowerUps.set(powerUp.type, powerUp);
                }
                
                // Show visual effect
                this.showPowerUpCollectEffect(collectible.position);
            } else {
                console.warn(`Failed to apply collected power-up ${powerUp.type}`);
            }
        } catch (error) {
            console.error('Error handling power-up collection:', error);
        }
    }

    /**
     * Show visual effect when collecting a power-up
     * @param {Vector2} position - Position where effect should appear
     */
    showPowerUpCollectEffect(position) {
        // Placeholder for visual effect - could be enhanced with particle system
        console.log(`Power-up collected at position: ${position.x}, ${position.y}`);
        // TODO: Add particle effect or visual indicator
    }

    /**
     * Set up token manager callbacks
     */
    setupTokenManagerCallbacks() {
        // Balance update callback
        this.tokenManager.setOnBalanceUpdate((balance, statistics) => {
            console.log(`Token balance updated: ${balance} WISH tokens`);
            // NOTE: OrangeSDK should NEVER be notified of token changes
            // Token management is handled exclusively by TokenEconomyManager
        });

        // Transaction callback
        this.tokenManager.setOnTransaction((transaction) => {
            console.log('Token transaction:', transaction);
            // NOTE: OrangeSDK should NEVER be notified of token changes
            // Token management is handled exclusively by TokenEconomyManager
        });

        // Reward earned callback
        this.tokenManager.setOnRewardEarned((amount, reason, metadata) => {
            console.log(`Token reward earned: ${amount} for ${reason}`);
        });
    }

    /**
     * Set up Genie interface callbacks
     */
    setupGenieInterfaceCallbacks() {
        // Power-up purchased callback
        this.genieInterface.setOnPowerUpPurchased((powerUp, transaction) => {
            console.log(`Power-up purchased: ${powerUp.type} for ${powerUp.cost} tokens`);

            // Update active power-ups tracking
            // The GenieInterface already handles the power-up application
        });

        // Reality warp purchased callback (for future implementation)
        this.genieInterface.setOnWarpPurchased((warpOption, transaction) => {
            console.log(`Reality warp purchased: ${warpOption.type} for ${warpOption.cost} tokens`);
        });

        // Consumable purchased callback
        this.genieInterface.setOnConsumablePurchased((consumable, transaction) => {
            console.log(`Consumable purchased: ${consumable.type} for ${consumable.cost} tokens`);
            // The ConsumableManager already handles the inventory management
        });

        // Interface closed callback
        this.genieInterface.setOnClose(() => {
            console.log('Genie interface closed, resuming game');
            this.continueToNextLevel();
        });
    }

    /**
     * Show the Genie interface between levels
     */
    showGenieInterface(completionData) {
        console.log('Showing Genie interface for level completion');

        // Store completion data for later use
        this.lastCompletionData = completionData;

        // Clear all active power-ups before showing the Genie interface
        this.clearPowerUps();

        // Pause the game
        this.pause();

        // Show the Genie interface
        this.genieInterface.show();
    }

    /**
     * Clear all active power-ups (called between levels)
     */
    clearPowerUps() {
        if (this.genieInterface && this.genieInterface.activePowerUps) {
            console.log('Clearing active power-ups for next level');

            const playerShip = this.playerShip;
            const weaponPowerUpTypes = ['KINETIC_BOOST', 'LASER_ROUNDS', 'FLAME_ROUNDS', 'ICE_ROUNDS', 'PLASMA_ROUNDS', 'SHADOW_ROUNDS'];
            const powerUpsToRemove = [];
            
            // Collect power-ups to remove (excluding weapon power-ups)
            for (const [type, powerUp] of this.genieInterface.activePowerUps) {
                if (!weaponPowerUpTypes.includes(type)) {
                    powerUpsToRemove.push([type, powerUp]);
                }
            }
            
            // Remove non-weapon power-ups
            for (const [type, powerUp] of powerUpsToRemove) {
                console.log(`Removing power-up: ${type}`);
                powerUp.remove(playerShip);
                this.genieInterface.activePowerUps.delete(type);
            }

            console.log('Non-weapon power-ups cleared, weapon power-ups preserved');
            
            // Re-synchronize weapon power-ups to ensure they're properly displayed
            this.syncWeaponPowerUps();
        }
    }

    /**
     * Synchronize weapon power-ups between GenieInterface and WeaponSystem
     * Ensures purchased weapon power-ups are properly applied and displayed
     */
    syncWeaponPowerUps() {
        if (!this.genieInterface || !this.playerShip || !this.playerShip.weaponSystem) {
            return;
        }

        const weaponPowerUpTypes = ['KINETIC_BOOST', 'LASER_ROUNDS', 'FLAME_ROUNDS', 'ICE_ROUNDS', 'PLASMA_ROUNDS', 'SHADOW_ROUNDS'];
        const currentVariant = this.playerShip.weaponSystem.currentVariant;
        
        // Find the power-up type that matches the current weapon variant
        const activeWeaponPowerUp = this.genieInterface.availablePowerUps.find(powerUp =>
            weaponPowerUpTypes.includes(powerUp.type) &&
            powerUp.weaponVariant === currentVariant
        );
        
        if (activeWeaponPowerUp) {
            // Add the active weapon power-up to the activePowerUps map for display
            this.genieInterface.activePowerUps.set(activeWeaponPowerUp.type, activeWeaponPowerUp);
            console.log(`Synchronized weapon power-up: ${activeWeaponPowerUp.type} for variant: ${currentVariant}`);
        }
    }

    /**
     * Continue to the next level after Genie interface is closed
     */
    continueToNextLevel() {
        if (this.lastCompletionData) {
            const completionData = this.lastCompletionData;
            this.lastCompletionData = null;

            // Resume the game
            this.resume();

            // Continue to next level or end game
            if (completionData.nextLevel <= this.levelManager.maxLevels) {
                console.log(`Starting level ${completionData.nextLevel}`);
                this.levelManager.startLevel(completionData.nextLevel);
            } else {
                console.log('Game completed! All levels finished.');
                // TODO: Show game completion screen
            }
        }
    }

    /**
     * Test power-up system (debug method)
     */
    testPowerUpSystem() {
        console.log('Testing power-up system...');

        // Ensure player has tokens
        if (this.tokenManager.getBalance() < 50000) {
            this.tokenManager.awardTokens(50000, 'debug_test_tokens');
            console.log('Added test tokens');
        }

        // Test each power-up type
        const powerUpTypes = ['EXTRA_LIFE', 'SPREAD_AMMO', 'EXTRA_WINGMAN'];

        powerUpTypes.forEach(async (type, index) => {
            setTimeout(async () => {
                console.log(`Testing ${type} power-up...`);

                if (this.genieInterface) {
                    await this.genieInterface.handlePowerUpPurchase(type);
                    console.log(`${type} test completed`);
                }
            }, index * 2000); // Stagger tests by 2 seconds
        });

        console.log('Power-up system test initiated');
    }

    /**
     * Activate current consumable
     */
    async activateConsumable() {
        console.log('🎯 activateConsumable called');
        if (!this.consumableManager) {
            console.error('❌ Consumable manager not available');
            return;
        }
        console.log('✅ Consumable manager found, proceeding with activation');

        console.log('🎮 Attempting to activate consumable...');
        const result = await this.consumableManager.activateConsumable();

        if (result.success) {
            console.log(`✅ Consumable activated: ${result.consumable.type}`);
            console.log(`🎯 Effect applied to game objects`);
            // Show visual feedback (could be enhanced with UI notifications later)
            if (this.ctx) {
                // Simple text feedback for now
                this.showConsumableActivationFeedback(result.consumable);
            }
        } else {
            console.log(`❌ Consumable activation failed: ${result.reason} - ${result.message || 'No message'}`);
            // Could show error message to player
        }
    }

    /**
     * Show visual feedback for consumable activation
     * @param {Consumable} consumable - The activated consumable
     */
    showConsumableActivationFeedback(consumable) {
        // This is a simple implementation - could be enhanced with better visuals
        const displayInfo = consumable.getDisplayInfo();
        console.log(`✨ ${displayInfo.name} activated! ${displayInfo.icon}`);

        // Could add particle effects, screen flash, or other visual feedback here
    }

    /**
     * Handle touch input for consumable activation button
     */
    handleConsumableTouchInput() {
        if (!this.consumableButtonBounds || !this.inputManager) return;

        // Check for touch start events
        for (const [touchId, touchPos] of this.inputManager.touchStarted) {
            if (this.isTouchInBounds(touchPos, this.consumableButtonBounds)) {
                // Touch started on consumable button
                this.activateConsumable();
                break; // Only handle one touch
            }
        }
    }

    /**
     * Check if touch position is within button bounds
     * @param {Vector2} touchPos - Touch position
     * @param {object} bounds - Button bounds {x, y, width, height}
     * @returns {boolean} True if touch is within bounds
     */
    isTouchInBounds(touchPos, bounds) {
        return touchPos.x >= bounds.x &&
               touchPos.x <= bounds.x + bounds.width &&
               touchPos.y >= bounds.y &&
               touchPos.y <= bounds.y + bounds.height;
    }

    /**
     * Initialize Orange SDK asynchronously
     */
    async initializeOrangeSDK() {
        try {
            await this.orangeSDKManager.initialize();
            console.log('Orange SDK initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Orange SDK:', error);
        }
    }

    /**
     * Set up Orange SDK manager callbacks
     */
    setupOrangeSDKCallbacks() {
        // Set up special status callback to handle bonuses
        this.orangeSDKManager.setOnSpecialStatusCallback((statusType, value) => {
            this.handleSpecialStatus(statusType, value);
        });

        // Set up save success callback
        this.orangeSDKManager.setOnDataSavedCallback((data, reason) => {
            console.log(`Game data saved to Orange SDK (${reason})`);
        });

        // Set up save error callback
        this.orangeSDKManager.setOnSaveErrorCallback((error, reason) => {
            console.error(`Failed to save game data (${reason}):`, error);
        });
    }

    /**
     * Handle special status bonuses from Orange SDK
     * @param {string} statusType - Type of special status
     * @param {*} value - Status value
     */
    handleSpecialStatus(statusType, value) {
        switch (statusType) {
            case 'bonus_lives':
                if (this.playerShip && value > 0) {
                    this.playerShip.addLives(value);
                    console.log(`Bonus lives awarded: ${value} (login streak bonus)`);
                }
                break;

            case 'tournament_participant':
                if (value) {
                    console.log('Tournament participant status active');
                    // Could add special tournament UI indicators or bonuses
                }
                break;

            case 'achievement_unlock':
                console.log(`Achievement unlocked: ${value}`);
                // Could trigger achievement notification UI
                break;

            default:
                console.log(`Unknown special status: ${statusType} = ${value}`);
        }
    }

    handleResize() {
        const container = document.getElementById('game-container');
        if (container) {
            const { width, height } = container.getBoundingClientRect();
            this.canvas.width = width;
            this.canvas.height = height;
            console.log(`Canvas resized to: ${this.canvas.width}x${this.canvas.height}`);

            // Resize bloom effect canvases
            if (this.bloomEffect) {
                this.bloomEffect.resize();
            }
        }
    }

    /**
     * Initialize bloom debug controls
     */
    initBloomDebugControls() {
        document.addEventListener('keydown', (event) => {
            if (!this.bloomEffect) return;

            switch(event.key.toLowerCase()) {
                case 'b':
                    // Toggle bloom effect
                    const enabled = this.bloomEffect.toggle();
                    console.log(`Bloom effect ${enabled ? 'enabled' : 'disabled'}`);
                    break;
                case '=':
                case '+':
                    // Increase bloom intensity
                    const currentIntensity = this.bloomEffect.getSettings().intensity;
                    this.bloomEffect.setIntensity(currentIntensity + 0.1);
                    console.log(`Bloom intensity: ${this.bloomEffect.getSettings().intensity.toFixed(1)}`);
                    break;
                case '-':
                case '_':
                    // Decrease bloom intensity
                    const intensity = this.bloomEffect.getSettings().intensity;
                    this.bloomEffect.setIntensity(intensity - 0.1);
                    console.log(`Bloom intensity: ${this.bloomEffect.getSettings().intensity.toFixed(1)}`);
                    break;
                case '[':
                    // Decrease bloom threshold
                    const threshold = this.bloomEffect.getSettings().threshold;
                    this.bloomEffect.setThreshold(threshold - 0.05);
                    console.log(`Bloom threshold: ${this.bloomEffect.getSettings().threshold.toFixed(2)}`);
                    break;
                case ']':
                    // Increase bloom threshold
                    const currentThreshold = this.bloomEffect.getSettings().threshold;
                    this.bloomEffect.setThreshold(currentThreshold + 0.05);
                    console.log(`Bloom threshold: ${this.bloomEffect.getSettings().threshold.toFixed(2)}`);
                    break;
            }
        });
    }
}