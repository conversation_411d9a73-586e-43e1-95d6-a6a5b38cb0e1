import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * TokenEconomyManager handles WISH token tracking, transactions, and rewards
 * Implements performance-based token calculation and balance management
 */
export class TokenEconomyManager {
    constructor() {
        // Token balance
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;

        // Transaction history
        this.transactionHistory = [];
        this.maxHistorySize = 100;

        // Performance tracking for rewards
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };

        // Load saved data from localStorage immediately
        this.loadFromStorage();
        
        // Token reward multipliers 
        this.rewardMultipliers = {
            base: 1.0,
            speed: 1.125,    // was 1.5, now 1.0 + (1.5 - 1.0) * 0.25 = 1.125
            accuracy: 1.075, // was 1.3, now 1.0 + (1.3 - 1.0) * 0.25 = 1.075
            perfect: 1.25,   // was 2.0, now 1.0 + (2.0 - 1.0) * 0.25 = 1.25
            difficulty: 1.0
        };
        
        // Visual feedback state
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        // Callbacks for UI updates
        this.onBalanceUpdateCallback = null;
        this.onTransactionCallback = null;
        this.onRewardEarnedCallback = null;
        
        // Debug mode
        this.debugMode = false;
        
    }
    
    /**
     * Calculate token reward based on level completion performance
     * @param {object} completionData - Level completion data from LevelManager
     * @returns {object} Token reward breakdown
     */
    calculateLevelReward(completionData) {
        if (!completionData.completed) {
            return { totalReward: 0, breakdown: { reason: 'level_not_completed' } };
        }
        
        const levelNumber = completionData.levelNumber;
        const completionTime = completionData.completionTime;
        const scoreData = completionData.score;
        
        // Base reward calculation
        const baseReward = this.calculateBaseReward(levelNumber);
        
        // Time-based bonus (inverse relationship - faster = more tokens)
        const timeBonus = this.calculateTimeBonus(completionTime, levelNumber);
        
        // Score-based bonus
        const scoreBonus = this.calculateScoreBonus(scoreData.totalScore, levelNumber);
        
        // Performance bonuses
        const speedBonus = completionData.bonuses.speed ? this.calculateSpeedBonus(baseReward) : 0;
        const accuracyBonus = completionData.bonuses.accuracy ? this.calculateAccuracyBonus(baseReward) : 0;
        const perfectBonus = completionData.bonuses.perfect ? this.calculatePerfectBonus(baseReward) : 0;
        
        // Difficulty multiplier
        const difficultyMultiplier = this.calculateDifficultyMultiplier(levelNumber);
        
        // Calculate total before multiplier
        const subtotal = baseReward + timeBonus + scoreBonus + speedBonus + accuracyBonus + perfectBonus;
        
        // Apply difficulty multiplier
        const totalReward = Math.floor(subtotal * difficultyMultiplier);
        
        const breakdown = {
            baseReward,
            timeBonus,
            scoreBonus,
            speedBonus,
            accuracyBonus,
            perfectBonus,
            difficultyMultiplier,
            subtotal,
            totalReward
        };
        
        
        return { totalReward, breakdown };
    }
    
    /**
     * Calculate base reward for level completion
     * @param {number} levelNumber - Level number
     * @returns {number} Base reward amount
     */
    calculateBaseReward(levelNumber) {
        const baseAmount = GAME_CONFIG.BASE_LEVEL_REWARD;
        const levelMultiplier = Math.floor((levelNumber - 1) / 5) + 1; // Increases every 5 levels
        return baseAmount * levelMultiplier;
    }
    
    /**
     * Calculate time bonus (faster completion = more tokens)
     * @param {number} completionTime - Time taken in seconds
     * @param {number} levelNumber - Level number
     * @returns {number} Time bonus amount
     */
    calculateTimeBonus(completionTime, levelNumber) {
        // Expected completion time increases with level
        const expectedTime = 60 + (levelNumber * 10); // Base 60s + 10s per level
        const fastTime = expectedTime * 0.5; // 50% of expected time for max bonus
        
        if (completionTime <= fastTime) {
            // Excellent time - maximum bonus 
            return Math.floor(750 + (levelNumber * 125));
        } else if (completionTime <= expectedTime * 0.75) {
            // Good time - partial bonus 
            return Math.floor(500 + (levelNumber * 75));
        } else if (completionTime <= expectedTime) {
            // Acceptable time - small bonus 
            return Math.floor(250 + (levelNumber * 25));
        }
        
        return 0; // No bonus for slow completion
    }
    
    /**
     * Calculate score bonus based on points earned
     * @param {number} score - Score achieved
     * @param {number} levelNumber - Level number
     * @returns {number} Score bonus amount
     */
    calculateScoreBonus(score, levelNumber) {
        // Score bonus is a percentage of the score, scaled by level 
        const basePercentage = 0.25; // 25% of score as base (reduced from 100% by 75%)
        const levelScaling = Math.min(2.0, 1.0 + (levelNumber * 0.05)); // Up to 2x scaling

        return Math.floor(score * basePercentage * levelScaling);
    }
    
    /**
     * Calculate speed bonus for fast completion
     * @param {number} baseReward - Base reward amount
     * @returns {number} Speed bonus amount
     */
    calculateSpeedBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.speed - 1.0));
    }
    
    /**
     * Calculate accuracy bonus for high accuracy
     * @param {number} baseReward - Base reward amount
     * @returns {number} Accuracy bonus amount
     */
    calculateAccuracyBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.accuracy - 1.0));
    }
    
    /**
     * Calculate perfect completion bonus
     * @param {number} baseReward - Base reward amount
     * @returns {number} Perfect bonus amount
     */
    calculatePerfectBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.perfect - 1.0));
    }
    
    /**
     * Calculate difficulty multiplier based on level
     * @param {number} levelNumber - Level number
     * @returns {number} Difficulty multiplier
     */
    calculateDifficultyMultiplier(levelNumber) {
        // Multiplier increases gradually with level
        const baseMultiplier = 1.0;
        const increment = 0.1;
        const maxMultiplier = 3.0;
        
        const multiplier = baseMultiplier + (Math.floor((levelNumber - 1) / 10) * increment);
        return Math.min(maxMultiplier, multiplier);
    }
    
    /**
     * Award tokens to player balance
     * @param {number} amount - Amount to award
     * @param {string} reason - Reason for the award
     * @param {object} metadata - Additional metadata
     * @returns {object} Transaction result
     */
    awardTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }
        
        // Update balance
        this.playerBalance += amount;
        this.totalEarned += amount;
        
        // Create transaction record
        const transaction = {
            id: this.generateTransactionId(),
            type: 'earned',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: metadata
        };
        
        // Add to history
        this.addTransaction(transaction);

        // Update performance metrics
        this.updatePerformanceMetrics(reason, metadata);

        // Add to pending rewards for visual feedback
        this.addPendingReward(amount, reason);

        // Save to localStorage
        this.saveToStorage();

        // Trigger callbacks
        this.triggerBalanceUpdate();
        this.triggerRewardEarned(amount, reason, metadata);
        
        return { 
            success: true, 
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Spend tokens from player balance
     * @param {number} amount - Amount to spend
     * @param {string} reason - Reason for spending
     * @param {object} metadata - Additional metadata
     * @returns {object} Transaction result
     */
    spendTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }
        
        if (this.playerBalance < amount) {
            return { success: false, reason: 'insufficient_balance', required: amount, available: this.playerBalance };
        }
        
        // Update balance
        this.playerBalance -= amount;
        this.totalSpent += amount;
        
        // Create transaction record
        const transaction = {
            id: this.generateTransactionId(),
            type: 'spent',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: metadata
        };
        
        // Add to history
        this.addTransaction(transaction);

        // Save to localStorage
        this.saveToStorage();

        // Trigger callbacks
        this.triggerBalanceUpdate();
        this.triggerTransaction(transaction);
        
        return { 
            success: true, 
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Check if player can afford a purchase
     * @param {number} amount - Amount to check
     * @returns {boolean} Whether player can afford the amount
     */
    canAfford(amount) {
        return this.playerBalance >= amount;
    }
    
    /**
     * Get current token balance
     * @returns {number} Current balance
     */
    getBalance() {
        return this.playerBalance;
    }
    
    /**
     * Get token economy statistics
     * @returns {object} Economy statistics
     */
    getStatistics() {
        const netProfit = this.totalEarned - this.totalSpent;
        const averageEarningPerLevel = this.performanceMetrics.levelsCompleted > 0 
            ? this.totalEarned / this.performanceMetrics.levelsCompleted 
            : 0;
        
        return {
            currentBalance: this.playerBalance,
            totalEarned: this.totalEarned,
            totalSpent: this.totalSpent,
            netProfit: netProfit,
            transactionCount: this.transactionHistory.length,
            averageEarningPerLevel: Math.floor(averageEarningPerLevel),
            performanceMetrics: { ...this.performanceMetrics }
        };
    }
    
    /**
     * Get recent transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Recent transactions
     */
    getRecentTransactions(count = 10) {
        return this.transactionHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Add transaction to history
     * @param {object} transaction - Transaction to add
     */
    addTransaction(transaction) {
        this.transactionHistory.push(transaction);
        
        // Limit history size
        if (this.transactionHistory.length > this.maxHistorySize) {
            this.transactionHistory.shift();
        }
    }
    
    /**
     * Generate unique transaction ID
     * @returns {string} Transaction ID
     */
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Update performance metrics
     * @param {string} reason - Reason for the update
     * @param {object} metadata - Additional metadata
     */
    updatePerformanceMetrics(reason, metadata) {
        if (reason === 'level_completion') {
            this.performanceMetrics.levelsCompleted++;
            
            if (metadata.score) {
                this.performanceMetrics.totalScore += metadata.score;
            }
            
            if (metadata.completionTime) {
                // Update average completion time
                const currentAvg = this.performanceMetrics.averageCompletionTime;
                const count = this.performanceMetrics.levelsCompleted;
                this.performanceMetrics.averageCompletionTime = 
                    (currentAvg * (count - 1) + metadata.completionTime) / count;
            }
            
            if (metadata.bonuses) {
                if (metadata.bonuses.perfect) this.performanceMetrics.perfectCompletions++;
                if (metadata.bonuses.speed) this.performanceMetrics.speedBonuses++;
                if (metadata.bonuses.accuracy) this.performanceMetrics.accuracyBonuses++;
            }
        }
    }
    
    /**
     * Add pending reward for visual feedback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reason for reward
     */
    addPendingReward(amount, reason) {
        const reward = {
            id: this.generateTransactionId(),
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            displayed: false
        };
        
        this.pendingRewards.push(reward);
    }
    
    /**
     * Get pending rewards for display
     * @returns {Array} Pending rewards
     */
    getPendingRewards() {
        return this.pendingRewards.filter(reward => !reward.displayed);
    }
    
    /**
     * Mark reward as displayed
     * @param {string} rewardId - Reward ID to mark as displayed
     */
    markRewardDisplayed(rewardId) {
        const reward = this.pendingRewards.find(r => r.id === rewardId);
        if (reward) {
            reward.displayed = true;
        }
        
        // Clean up old displayed rewards
        const cutoffTime = Date.now() - 10000; // 10 seconds
        this.pendingRewards = this.pendingRewards.filter(
            reward => !reward.displayed || reward.timestamp > cutoffTime
        );
    }
    
    /**
     * Update reward animations
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateRewardAnimations(deltaTime) {
        // Update existing animations
        for (let i = this.rewardAnimations.length - 1; i >= 0; i--) {
            const animation = this.rewardAnimations[i];
            animation.elapsed += deltaTime;
            
            // Remove completed animations
            if (animation.elapsed >= animation.duration) {
                this.rewardAnimations.splice(i, 1);
            }
        }
        
        // Add new animations for pending rewards
        const pendingRewards = this.getPendingRewards();
        for (const reward of pendingRewards) {
            this.rewardAnimations.push({
                id: reward.id,
                amount: reward.amount,
                reason: reward.reason,
                startTime: Date.now(),
                elapsed: 0,
                duration: 3000, // 3 second animation
                startY: 100,
                endY: 50,
                alpha: 1.0
            });
            
            this.markRewardDisplayed(reward.id);
        }
    }
    
    /**
     * Render token balance and reward animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} deltaTime - Time elapsed since last update
     */
    render(ctx, deltaTime) {
        // Update animations
        this.updateRewardAnimations(deltaTime);
        
        // Render reward animations (without token balance)
        this.renderRewardAnimations(ctx);
    }
    
    /**
     * Render token balance display
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderTokenBalance(ctx) {
        const x = ctx.canvas.width - 20;
        const y = 80;
        const panelWidth = 160;
        const panelHeight = 40;
        const panelX = x - panelWidth - 10;
        const panelY = y - panelHeight / 2;
        
        // Create sci-fi background panel with gradient
        const panelGradient = ctx.createLinearGradient(panelX, panelY, panelX + panelWidth, panelY + panelHeight);
        panelGradient.addColorStop(0, 'rgba(30, 0, 46, 0.8)');
        panelGradient.addColorStop(1, 'rgba(10, 0, 30, 0.8)');
        ctx.fillStyle = panelGradient;
        ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
        
        // Panel border with sci-fi glow
        ctx.strokeStyle = '#ff00ff';
        ctx.lineWidth = 2;
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 15;
        ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
        
        // Inner border for depth
        ctx.strokeStyle = 'rgba(255, 0, 255, 0.3)';
        ctx.lineWidth = 1;
        ctx.strokeRect(panelX + 5, panelY + 5, panelWidth - 10, panelHeight - 10);
        ctx.shadowBlur = 0;
        
        // Token icon (simple hexagon)
        const iconSize = 12;
        const iconX = panelX + 15;
        const iconY = panelY + panelHeight / 2;
        ctx.fillStyle = '#ff00ff';
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (Math.PI / 3) * i;
            const pointX = iconX + iconSize * Math.cos(angle);
            const pointY = iconY + iconSize * Math.sin(angle);
            if (i === 0) {
                ctx.moveTo(pointX, pointY);
            } else {
                ctx.lineTo(pointX, pointY);
            }
        }
        ctx.closePath();
        ctx.fill();
        ctx.shadowBlur = 0;
        
        // Token text with sci-fi styling
        ctx.fillStyle = '#ff00ff';
        ctx.font = 'bold 16px "Courier New", monospace';
        ctx.textAlign = 'right';
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 8;
        ctx.fillText(`WISH: ${this.playerBalance}`, x - 5, y + 5);
        ctx.shadowBlur = 0;
    }
    
    /**
     * Render reward animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderRewardAnimations(ctx) {
        const x = ctx.canvas.width - 20;
        
        for (const animation of this.rewardAnimations) {
            const progress = animation.elapsed / animation.duration;
            const currentY = animation.startY + (animation.endY - animation.startY) * progress;
            const alpha = 1.0 - progress;
            
            // Pulsing glow effect
            const pulseIntensity = 0.7 + 0.3 * Math.sin(Date.now() / 100);
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // Sci-fi text styling for reward
            ctx.fillStyle = '#ff00ff';
            ctx.font = 'bold 20px "Courier New", monospace';
            ctx.textAlign = 'right';
            ctx.shadowColor = '#ff00ff';
            ctx.shadowBlur = 15 * pulseIntensity;
            ctx.fillText(`+${animation.amount} WISH`, x, currentY);
            
            // Add decorative hexagon icon that scales with animation
            const iconSize = 10 * (1 + progress * 0.5); // Grows as it moves up
            const iconX = x + 10;
            const iconY = currentY - 5;
            
            ctx.beginPath();
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI / 3) * i;
                const pointX = iconX + iconSize * Math.cos(angle);
                const pointY = iconY + iconSize * Math.sin(angle);
                if (i === 0) {
                    ctx.moveTo(pointX, pointY);
                } else {
                    ctx.lineTo(pointX, pointY);
                }
            }
            ctx.closePath();
            ctx.strokeStyle = '#ff00ff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.restore();
        }
    }
    
    /**
     * Format reward reason for display
     * @param {string} reason - Internal reason code
     * @returns {string} Display-friendly reason
     */
    formatRewardReason(reason) {
        const reasonMap = {
            'level_completion': 'Level Complete',
            'speed_bonus': 'Speed Bonus',
            'accuracy_bonus': 'Accuracy Bonus',
            'perfect_bonus': 'Perfect Clear',
            'daily_bonus': 'Daily Bonus',
            'achievement': 'Achievement'
        };
        
        return reasonMap[reason] || reason;
    }
    
    /**
     * Reset token economy (for new game)
     */
    reset() {
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;
        this.transactionHistory = [];
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };
        
        
    }
    
    /**
     * Trigger balance update callback
     */
    triggerBalanceUpdate() {
        if (this.onBalanceUpdateCallback) {
            this.onBalanceUpdateCallback(this.playerBalance);
        }
    }
    
    /**
     * Trigger transaction callback
     * @param {object} transaction - Transaction data
     */
    triggerTransaction(transaction) {
        if (this.onTransactionCallback) {
            this.onTransactionCallback(transaction);
        }
    }
    
    /**
     * Trigger reward earned callback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reward reason
     * @param {object} metadata - Additional metadata
     */
    triggerRewardEarned(amount, reason, metadata) {
        if (this.onRewardEarnedCallback) {
            this.onRewardEarnedCallback(amount, reason, metadata);
        }
    }
    
    /**
     * Set balance update callback
     * @param {function} callback - Callback function
     */
    setOnBalanceUpdate(callback) {
        this.onBalanceUpdateCallback = callback;
    }
    
    /**
     * Set transaction callback
     * @param {function} callback - Callback function
     */
    setOnTransaction(callback) {
        this.onTransactionCallback = callback;
    }
    
    /**
     * Set reward earned callback
     * @param {function} callback - Callback function
     */
    setOnRewardEarned(callback) {
        this.onRewardEarnedCallback = callback;
    }
    
    /**
     * Log cost calculation for debugging
     * @private
     * @param {string} warpType - Type of warp
     * @param {number} cost - Calculated cost
     * @param {object} calculationDetails - Details of the calculation
     */
    logCostCalculation(warpType, cost, calculationDetails = {}) {
        if (!this.debugMode) return;
        
        const logEntry = {
            timestamp: Date.now(),
            warpType: warpType,
            cost: cost,
            details: calculationDetails,
            balance: this.playerBalance
        };
        
        
    }
    
    /**
     * Track token transaction
     * @private
     * @param {object} transaction - Transaction data
     */
    trackTransaction(transaction) {
        if (!this.debugMode) return;
    }
    
    /**
     * Log validation result
     * @private
     * @param {string} type - Validation type
     * @param {object} result - Validation result
     */
    logValidation(type, result) {
        if (!this.debugMode) return;
    }
    
    /**
     * Get performance metrics for cost calculations
     * @returns {object} Cost calculation metrics
     */
    getCostCalculationMetrics() {
        return {
            recentTransactions: this.getRecentTransactions(5)
                .map(tx => ({
                    type: tx.type,
                    amount: tx.amount,
                    reason: tx.reason,
                    timestamp: tx.timestamp
                })),
            currentBalance: this.playerBalance,
            performanceMetrics: { ...this.performanceMetrics }
        };
    }
    
    /**
     * Get transaction metrics for analysis
     * @returns {object} Transaction metrics
     */
    getTransactionMetrics() {
        const earnedTransactions = this.transactionHistory.filter(tx => tx.type === 'earned');
        const spentTransactions = this.transactionHistory.filter(tx => tx.type === 'spent');
        
        return {
            totalTransactions: this.transactionHistory.length,
            earnedTransactions: earnedTransactions.length,
            spentTransactions: spentTransactions.length,
            averageEarnAmount: earnedTransactions.length > 0 
                ? earnedTransactions.reduce((sum, tx) => sum + tx.amount, 0) / earnedTransactions.length 
                : 0,
            averageSpendAmount: spentTransactions.length > 0 
                ? spentTransactions.reduce((sum, tx) => sum + tx.amount, 0) / spentTransactions.length 
                : 0
        };
    }
    
    /**
     * Get all performance metrics
     * @returns {object} All performance metrics
     */
    getAllPerformanceMetrics() {
        return {
            tokenMetrics: this.getStatistics(),
            transactionMetrics: this.getTransactionMetrics(),
            costCalculationMetrics: this.getCostCalculationMetrics(),
            systemState: {
                debugMode: this.debugMode,
                pendingRewards: this.pendingRewards.length,
                activeAnimations: this.rewardAnimations.length
            }
        };
    }
    
    /**
     * Set debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;

    }

    /**
     * Load token data from localStorage
     */
    loadFromStorage() {
        try {
            const data = localStorage.getItem('tokenEconomyManager');
            if (!data) return;

            const parsed = JSON.parse(data);

            this.playerBalance = parsed.playerBalance || 0;
            this.totalEarned = parsed.totalEarned || 0;
            this.totalSpent = parsed.totalSpent || 0;
            this.transactionHistory = parsed.transactionHistory || [];
            this.performanceMetrics = { ...this.performanceMetrics, ...(parsed.performanceMetrics || {}) };

            console.log(`Loaded token data: ${this.playerBalance} WISH tokens`);
        } catch (error) {
            console.warn('Failed to load token data from localStorage:', error);
        }
    }

    /**
     * Save token data to localStorage
     */
    saveToStorage() {
        try {
            const data = {
                playerBalance: this.playerBalance,
                totalEarned: this.totalEarned,
                totalSpent: this.totalSpent,
                transactionHistory: this.transactionHistory.slice(-this.maxHistorySize), // Keep only recent transactions
                performanceMetrics: this.performanceMetrics
            };

            localStorage.setItem('tokenEconomyManager', JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save token data to localStorage:', error);
        }
    }
    
    /**
     * Export debug data
     * @returns {object} Debug data
     */
    exportDebugData() {
        return {
            balance: this.playerBalance,
            totals: {
                earned: this.totalEarned,
                spent: this.totalSpent
            },
            recentTransactions: this.getRecentTransactions(10),
            performanceMetrics: { ...this.performanceMetrics },
            pendingRewards: [...this.pendingRewards],
            activeAnimations: [...this.rewardAnimations]
        };
    }
    
    /**
     * Log system state
     */
    logSystemState() {
        if (!this.debugMode) return;
    }
    
    /**
     * Calculate base cost for warp type and level
     * @param {string} warpType - Type of warp (basic, advanced, ultimate)
     * @param {number} level - Current level
     * @returns {number} Base cost amount
     */
    calculateBaseCost(warpType, level = 1) {
        const warpCosts = {
            'basic': GAME_CONFIG.WARP_BASE_COST,
            'advanced': GAME_CONFIG.WARP_BASE_COST * 2,
            'ultimate': GAME_CONFIG.WARP_BASE_COST * 4
        };
        
        const baseCost = warpCosts[warpType] || GAME_CONFIG.WARP_BASE_COST;
        
        // Apply level-based scaling
        const levelMultiplier = 1 + (level - 1) * 0.1;
        const scaledCost = baseCost * levelMultiplier;
        
        
        return Math.floor(scaledCost);
    }
    
    /**
     * Apply session discount to cost
     * @param {number} baseCost - Base cost amount
     * @param {number} warpCount - Number of warps used in current session
     * @returns {number} Discounted cost
     */
    applySessionDiscount(baseCost, warpCount = 0) {
        // No discount for first warp, increasing discount for subsequent warps
        const discountRate = Math.min(0.5, warpCount * 0.1); // Max 50% discount
        const discountedCost = baseCost * (1 - discountRate);
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return Math.floor(discountedCost);
    }
    
    /**
     * Apply performance multiplier to cost
     * @param {number} cost - Current cost
     * @param {object} playerStats - Player performance statistics
     * @returns {number} Adjusted cost
     */
    applyPerformanceMultiplier(cost, playerStats) {
        // Better performance = lower cost
        const accuracyMultiplier = Math.max(0.5, 1.0 - (playerStats.accuracy - 0.5) * 0.5);
        const scoreMultiplier = Math.max(0.7, 1.0 - (playerStats.score / 50000) * 0.3);
        const enemiesMultiplier = Math.max(0.8, 1.0 - (playerStats.enemiesDefeated / 100) * 0.2);
        
        const finalMultiplier = accuracyMultiplier * scoreMultiplier * enemiesMultiplier;
        const adjustedCost = cost * finalMultiplier;
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return Math.floor(adjustedCost);
    }
    
    /**
     * Get final cost for warp operation
     * @param {string} warpType - Type of warp
     * @param {number} level - Current level
     * @param {number} warpCount - Number of warps used in session
     * @param {object} playerStats - Player performance statistics
     * @returns {object} Final cost calculation
     */
    getFinalCost(warpType, level, warpCount, playerStats) {
        const baseCost = this.calculateBaseCost(warpType, level);
        const sessionCost = this.applySessionDiscount(baseCost, warpCount);
        const finalCost = this.applyPerformanceMultiplier(sessionCost, playerStats);
        
        const calculationDetails = {
            baseCost: baseCost,
            sessionDiscount: baseCost - sessionCost,
            performanceAdjustment: sessionCost - finalCost,
            warpCount: warpCount,
            playerStats: playerStats
        };
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        this.logCostCalculation(warpType, finalCost, calculationDetails);
        
        return {
            cost: finalCost,
            details: calculationDetails
        };
    }
    
    /**
     * Validate token balance for warp operation
     * @param {number} cost - Cost of operation
     * @returns {object} Validation result
     */
    validateTokenBalance(cost) {
        const hasBalance = this.canAfford(cost);
        
        const result = {
            isValid: hasBalance,
            reason: hasBalance ? 'valid' : 'insufficient_balance',
            message: hasBalance 
                ? 'Sufficient tokens for operation' 
                : `Insufficient tokens. Required: ${cost}, Available: ${this.playerBalance}`,
            required: cost,
            available: this.playerBalance
        };
        
        this.logValidation('Token Balance', result);
        return result;
    }
    
    /**
     * Process warp transaction
     * @param {string} warpType - Type of warp
     * @param {number} cost - Cost of operation
     * @returns {object} Transaction result
     */
    processWarpTransaction(warpType, cost) {
        const validation = this.validateTokenBalance(cost);
        
        if (!validation.isValid) {
            return {
                success: false,
                reason: validation.reason,
                message: validation.message,
                validation: validation
            };
        }
        
        // Create pre-transaction snapshot for potential rollback
        const preTransaction = {
            balance: this.playerBalance,
            totalSpent: this.totalSpent
        };
        
        // Process the transaction
        const spendResult = this.spendTokens(cost, `warp_${warpType}`, {
            warpType: warpType,
            costBreakdown: cost
        });
        
        if (!spendResult.success) {
            return {
                success: false,
                reason: spendResult.reason,
                message: 'Failed to process token transaction'
            };
        }
        
        // Create transaction record for RealityWarpManager
        const transaction = {
            id: spendResult.transaction.id,
            type: 'warp',
            warpType: warpType,
            cost: cost,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: {
                preTransactionBalance: preTransaction.balance,
                validation: validation
            }
        };
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return {
            success: true,
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Refund warp transaction
     * @param {string} warpType - Type of warp
     * @param {number} cost - Cost to refund
     * @param {string} reason - Reason for refund
     * @returns {object} Refund result
     */
    refundWarpTransaction(warpType, cost, reason = 'warp_refund') {
        if (cost <= 0) {
            return {
                success: false,
                reason: 'invalid_amount',
                message: 'Refund amount must be positive'
            };
        }
        
        const awardResult = this.awardTokens(cost, `warp_refund_${warpType}`, {
            originalCost: cost,
            refundReason: reason
        });
        
        if (!awardResult.success) {
            return {
                success: false,
                reason: awardResult.reason,
                message: 'Failed to process token refund'
            };
        }
        
        return {
            success: true,
            transaction: awardResult.transaction,
            newBalance: this.playerBalance,
            refundedAmount: cost
        };
    }
    
    /**
     * Rollback transaction
     * @param {object} transaction - Transaction to rollback
     * @returns {object} Rollback result
     */
    rollbackTransaction(transaction) {
        if (transaction.type === 'spent') {
            // Refund the spent amount
            return this.refundWarpTransaction(
                transaction.metadata.warpType || 'unknown',
                transaction.amount,
                'transaction_rollback'
            );
        } else if (transaction.type === 'earned') {
            // Deduct the awarded amount
            const spendResult = this.spendTokens(
                transaction.amount,
                'rollback_earned',
                { originalTransactionId: transaction.id }
            );
            
            if (!spendResult.success) {
                return {
                    success: false,
                    reason: spendResult.reason,
                    message: 'Failed to rollback earned transaction'
                };
            }
            
            return {
                success: true,
                transaction: spendResult.transaction,
                newBalance: this.playerBalance
            };
        }
        
        return {
            success: false,
            reason: 'invalid_transaction_type',
            message: 'Cannot rollback transaction type: ' + transaction.type
        };
    }
    
    /**
     * Get transaction history for RealityWarpManager
     * @returns {Array} Filtered transaction history
     */
    getWarpTransactionHistory() {
        return this.transactionHistory
            .filter(tx => tx.type === 'spent' && tx.reason.startsWith('warp_'))
            .reverse();
    }
    
    /**
     * Get warp cost statistics
     * @returns {object} Cost statistics
     */
    getWarpCostStatistics() {
        const warpTransactions = this.getWarpTransactionHistory();
        
        if (warpTransactions.length === 0) {
            return {
                totalWarps: 0,
                averageCost: 0,
                totalSpent: 0,
                mostExpensive: 0,
                leastExpensive: 0
            };
        }
        
        const costs = warpTransactions.map(tx => tx.amount);
        const totalSpent = costs.reduce((sum, cost) => sum + cost, 0);
        
        return {
            totalWarps: warpTransactions.length,
            averageCost: Math.floor(totalSpent / warpTransactions.length),
            totalSpent: totalSpent,
            mostExpensive: Math.max(...costs),
            leastExpensive: Math.min(...costs)
        };
    }
    
    /**
     * Get transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Transaction history
     */
    getTransactionHistory(count = 20) {
        return this.transactionHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Deduct tokens for warp with proper validation (alias for spendTokens)
     * @param {number} cost - Cost to deduct
     * @param {string} warpType - Type of warp
     * @returns {object} Transaction result
     */
    deductTokens(cost, warpType) {
        return this.spendTokens(cost, `warp_${warpType}`, { warpType: warpType });
    }
}