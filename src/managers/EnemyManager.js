import { Enemy } from '../entities/Enemy.js';
import { Boss } from '../entities/Boss.js';
import { Vector2 } from '../utils/Vector2.js';
import { GameMath } from '../utils/GameMath.js';
import { ENEMY_TYPES, GAME_CONFIG } from '../config/gameConfig.js';
import { scaleEnemyStats, shouldBeElite, getAvailableBehaviors, getTierForLevel } from '../utils/EnemyScaling.js';
import { EnemyProjectileSystem } from '../systems/EnemyProjectileSystem.js';

/**
 * EnemyManager handles enemy spawning, wave management, and lifecycle
 * Manages collision detection between enemies and player/projectiles
 */
export class EnemyManager {
    constructor(canvasWidth = GAME_CONFIG.CANVAS_WIDTH, canvasHeight = GAME_CONFIG.CANVAS_HEIGHT, gameObjectManager = null) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.gameObjectManager = gameObjectManager;

        // Enemy tracking
        this.activeEnemies = [];
        this.enemyPool = []; // Object pool for performance
        this.maxEnemies = 50;
        
        // Boss tracking
        this.activeBosses = [];
        this.bossPool = []; // Object pool for bosses
        this.maxBosses = 3;
        this.currentBoss = null;
        this.bossEncounterActive = false;

        // Wave management
        this.currentWave = 0;
        this.waveInProgress = false;
        this.waveStartTime = 0;
        this.waveConfig = null;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;
        this.enemiesEscapedInWave = 0;
        this.patternSpawnCounts = {}; // Track how many enemies spawned per pattern
        
        // Boss encounter management
        this.bossEncounterCooldown = 30000; // 30 seconds between boss encounters
        this.lastBossEncounterTime = 0;
        this.bossDefeatCallback = null;
        this.bossWarpCallback = null;

        // Spawn timing
        this.lastSpawnTime = 0;
        this.spawnCooldown = 1000; // Base spawn cooldown in milliseconds
        this.spawnTimer = 0;

        // Formation management
        this.formations = [];
        this.formationSpawnQueue = [];

        // Dive attack system (Space Invaders style)
        this.nextDiveTime = 2.5; // Time until next dive attack
        this.diveAttackCooldown = 2.0; // Base cooldown between dive attacks
        this.divingEnemies = new Set(); // Track enemies currently diving

        // Environmental effects
        this.currentEnvironment = 'space';
        this.environmentalEffects = null; // Will be set by setEnvironment()

        // Statistics
        this.totalEnemiesSpawned = 0;
        this.totalEnemiesKilled = 0;
        this.totalScore = 0;

        // Collision detection optimization
        this.collisionGrid = null;
        this.gridSize = 64;
        this.gridWidth = Math.ceil(canvasWidth / this.gridSize);
        this.gridHeight = Math.ceil(canvasHeight / this.gridSize);

        // Enemy projectile system for enemy firing capabilities
        this.projectileSystem = new EnemyProjectileSystem(canvasWidth, canvasHeight, gameObjectManager);

    }
    
    /**
     * Set the BossWarpManager
     * @param {BossWarpManager} bossWarpManager - BossWarpManager instance
     */
    setBossWarpManager(bossWarpManager) {
        this.bossWarpManager = bossWarpManager;
    }

    /**
     * Set the Environment instance for applying environmental effects
     * @param {Environment} environment - Environment instance
     */
    setEnvironment(environment) {
        this.environmentalEffects = environment;
        
        // Update all active enemies with the new environment
        for (const enemy of this.activeEnemies) {
            this.applyEnvironmentalEffects(enemy);
        }
        
        // Update all active bosses with the new environment
        for (const boss of this.activeBosses) {
            this.applyEnvironmentalEffects(boss);
        }
    }
    
    /**
     * Get environment data from level configuration
     * @returns {object|null} Environment data or null if not available
     */
    getLevelEnvironmentData() {
        if (window.gameEngine && window.gameEngine.levelManager &&
            window.gameEngine.levelManager.levelConfig &&
            window.gameEngine.levelManager.levelConfig.environmentData) {
            return window.gameEngine.levelManager.levelConfig.environmentData;
        }
        return null;
    }

    /**
     * Update all enemies and wave management
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    update(deltaTime, playerPosition = null) {
        // Update spawn timer
        this.spawnTimer += deltaTime;

        // Update wave management
        this.updateWaveManagement(deltaTime);

        // Spawn enemies based on current wave
        this.updateEnemySpawning(deltaTime, playerPosition);

        // Update all active enemies
        this.updateActiveEnemies(deltaTime, playerPosition);

        // Update all active bosses
        this.updateActiveBosses(deltaTime, playerPosition);

        // Clean up destroyed enemies
        this.cleanupDestroyedEnemies();

        // Clean up destroyed bosses
        this.cleanupDestroyedBosses();

        // Update formations
        this.updateFormations(deltaTime);

        // Update dive attacks (Space Invaders style)
        this.updateDiveAttacks(deltaTime, playerPosition);

        // Update enemy projectile system (Space Invaders style firing)
        if (this.projectileSystem) {
            this.projectileSystem.update(deltaTime, playerPosition);
        }

        // Update enemy attacks (Space Invaders style firing patterns)
        this.updateEnemyAttacks(deltaTime, playerPosition);

        // Check wave completion
        this.checkWaveCompletion();
        
        // Check for boss encounters
        this.checkBossEncounter();
    }

    /**
     * Update wave management logic
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateWaveManagement(deltaTime) {
        if (!this.waveInProgress && this.activeEnemies.length === 0) {
            // Start next wave if no enemies are active
            this.startNextWave();
        }

        if (this.waveInProgress) {
            this.waveStartTime += deltaTime;
        }
    }

    /**
     * Update enemy spawning based on current wave configuration
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateEnemySpawning(deltaTime, playerPosition) {
        if (!this.waveInProgress || !this.waveConfig) return;

        // For Space Invaders style, spawn all enemies at once when wave starts
        if (this.enemiesSpawnedInWave === 0) {
            this.spawnEntireWaveFormation();
        }
    }

    /**
     * Spawn the entire wave formation at once (Space Invaders style)
     */
    spawnEntireWaveFormation() {
        if (!this.waveConfig) return;

        // Create the formation
        const formation = this.getOrCreateFormation('space_invaders_grid');

        // Get grid configuration from wave config
        const cols = this.waveConfig.gridCols || 8;
        const rows = this.waveConfig.gridRows || 3;
        const rowTypes = this.waveConfig.spawnPatterns[0]?.rowTypes || [];

        // Spawn all enemies for the wave
        for (let i = 0; i < this.waveConfig.totalEnemies; i++) {
            // Calculate grid position
            const gridRow = Math.floor(i / cols);
            const gridCol = i % cols;

            // Select enemy type based on row (Space Invaders style)
            let enemyType;
            if (rowTypes.length > gridRow) {
                enemyType = rowTypes[gridRow];
            } else {
                // Fallback to random selection
                enemyType = this.selectEnemyType(this.waveConfig.enemyTypes);
            }

            const spacing = 60;
            const rowSpacing = 50;

            // Calculate spawn position relative to formation center
            const gridWidth = (cols - 1) * spacing;
            const startX = formation.center.x - gridWidth / 2;
            const spawnX = startX + gridCol * spacing;
            const spawnY = formation.center.y + gridRow * rowSpacing;

            // Spawn the enemy
            const enemy = this.spawnEnemy(spawnX, spawnY, enemyType);

            if (enemy) {
                // Add to formation
                const offset = this.calculateFormationOffset('space_invaders_grid', i);
                enemy.setMovementPattern('formation');
                enemy.setFormationTarget(formation.center, offset);
                formation.enemies.push(enemy);

                // Set enemy row information for special behaviors
                enemy.gridRow = gridRow;
                enemy.gridCol = gridCol;

                this.enemiesSpawnedInWave++;
            }
        }

        console.log(`Spawned Space Invaders formation: ${cols}x${rows} grid with ${this.enemiesSpawnedInWave} enemies`);

        // Visual effect for formation spawn
        this.createFormationSpawnEffect(formation.center, cols, rows);
    }

    /**
     * Update all active enemies
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateActiveEnemies(deltaTime, playerPosition) {
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            if (enemy.active) {
                enemy.update(deltaTime, playerPosition);
                
                // Environmental effects are now applied in Enemy constructor and reset method
                // No need to apply them here during update
            }
        }
    }
    
    /**
     * Update all active bosses
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateActiveBosses(deltaTime, playerPosition) {
        for (let i = this.activeBosses.length - 1; i >= 0; i--) {
            const boss = this.activeBosses[i];
            
            if (boss.active) {
                boss.update(deltaTime, playerPosition);
                
                // Environmental effects are now applied in Enemy constructor and reset method
                // No need to apply them here during update
                
                // Check for boss warp triggers
                this.checkBossWarpTriggers(boss);
            }
        }
    }

    /**
     * Clean up destroyed or inactive enemies
     */
    cleanupDestroyedEnemies() {
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            if (enemy.destroyed || !enemy.active) {
                // Track whether enemy was killed or escaped
                if (enemy.isDestroyed) {
                    // Enemy was killed by player
                    this.enemiesKilledInWave++;
                    this.totalEnemiesKilled++;
                    this.totalScore += enemy.scoreValue;
                } else if (enemy.destroyed) {
                    // Enemy escaped off-screen
                    this.enemiesEscapedInWave++;

                    // Notify game about enemy escape
                    this.onEnemyEscaped(enemy);
                }

                // Return enemy to pool
                this.returnEnemyToPool(enemy);
                this.activeEnemies.splice(i, 1);
            }
        }
    }
    
    /**
     * Clean up destroyed or inactive bosses
     */
    cleanupDestroyedBosses() {
        for (let i = this.activeBosses.length - 1; i >= 0; i--) {
            const boss = this.activeBosses[i];

            if (boss.destroyed || !boss.active) {
                // Track whether boss was killed or escaped
                if (boss.isDestroyed) {
                    // Boss was killed by player
                    this.totalEnemiesKilled++;
                    this.totalScore += boss.scoreValue;
                    console.log(`Boss "${boss.bossName}" defeated! Score: ${boss.scoreValue}`);
                    
                    // Trigger boss defeat callback
                    if (this.bossDefeatCallback) {
                        this.bossDefeatCallback(boss);
                    }
                    
                    // End boss encounter
                    this.endBossEncounter();
                } else if (boss.destroyed) {
                    // Boss escaped off-screen
                    console.log(`Boss "${boss.bossName}" escaped`);
                    
                    // End boss encounter
                    this.endBossEncounter();
                }

                // Return boss to pool
                this.returnBossToPool(boss);
                this.activeBosses.splice(i, 1);
                
                // Clear current boss reference
                if (this.currentBoss === boss) {
                    this.currentBoss = null;
                }
            }
        }
    }

    /**
     * Start the next wave
     */
    startNextWave() {
        this.currentWave++;
        this.waveInProgress = true;
        this.waveStartTime = 0;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;
        this.enemiesEscapedInWave = 0;
        this.patternSpawnCounts = {}; // Reset pattern tracking

        // Generate wave configuration
        this.waveConfig = this.generateWaveConfig(this.currentWave);

        // Initialize pattern spawn counts
        if (this.waveConfig.spawnPatterns) {
            for (let i = 0; i < this.waveConfig.spawnPatterns.length; i++) {
                this.patternSpawnCounts[i] = 0;
            }
        }

        // Set spawn cooldown based on wave difficulty
        this.spawnCooldown = Math.max(200, 1500 - (this.currentWave * 50));

        console.log(`Starting wave ${this.currentWave}:`, this.waveConfig);
    }

    /**
     * Generate wave configuration based on wave number
     * @param {number} waveNumber - Current wave number
     * @returns {object} Wave configuration
     */
    generateWaveConfig(waveNumber) {
        // Space Invaders-style grid calculation
        const cols = Math.min(10, 6 + waveNumber); // Start with 6 columns, increase with wave
        const rows = Math.min(5, 3 + Math.floor(waveNumber / 2)); // Start with 3 rows, increase every 2 waves
        let totalEnemies = cols * rows;

        // Apply environmental modifiers to enemy spawn count
        const environmentData = window.gameEngine?.levelManager?.levelConfig?.environmentData;
        const gameplayModifiers = environmentData?.gameplayModifiers;

        if (gameplayModifiers) {
            // Apply enhanced general spawn rate multiplier (50% more pronounced)
            if (gameplayModifiers.enemySpawnRateMultiplier) {
                const enhancedSpawnRateMultiplier = 1 + (gameplayModifiers.enemySpawnRateMultiplier - 1) * 1.5;
                totalEnemies = Math.round(totalEnemies * enhancedSpawnRateMultiplier);
            }

            // Apply enemy type modifiers to spawn counts (these will be enhanced in the method)
            if (gameplayModifiers.enemyTypeModifiers) {
                totalEnemies = this.applyEnemyTypeModifiersToSpawnCount(totalEnemies, gameplayModifiers.enemyTypeModifiers, waveNumber);
            }
        }

        // Determine enemy type distribution based on wave
        const enemyTypes = this.getSpaceInvadersEnemyTypes(waveNumber);

        // Create Space Invaders spawn patterns with modified enemy count
        const spawnPatterns = this.getSpaceInvadersWavePattern(waveNumber, totalEnemies, cols, rows);

        return {
            waveNumber: waveNumber,
            totalEnemies: totalEnemies,
            gridCols: cols,
            gridRows: rows,
            enemyTypes: enemyTypes,
            spawnPatterns: spawnPatterns,
            difficulty: Math.min(10, Math.floor(waveNumber / 3) + 1),
            hasFormation: true, // Always use formation for Space Invaders style
            hasBoss: waveNumber % 8 === 0, // Boss every 8th wave
            // Space Invaders specific properties
            formationSpeed: 40 + (waveNumber * 6), // Increase speed with wave
            descentAmount: 25 + (waveNumber * 2), // Increase descent with wave
            fireRate: Math.max(0.6, 2.0 - (waveNumber * 0.1)), // Increase fire rate with wave
            diveFrequency: Math.max(0.8, 2.5 - (waveNumber * 0.15)) // More frequent dives with wave
        };
    }

    /**
     * Apply enemy type modifiers to spawn count
     * @param {number} baseSpawnCount - Base number of enemies to spawn
     * @param {object} enemyTypeModifiers - Enemy type modifiers from environment
     * @param {number} waveNumber - Current wave number
     * @returns {number} Modified spawn count
     */
    applyEnemyTypeModifiersToSpawnCount(baseSpawnCount, enemyTypeModifiers, waveNumber) {
        // Get the enemy types that would appear in this wave
        const enemyTypes = this.getSpaceInvadersEnemyTypes(waveNumber);

        // Calculate weighted average of type modifiers based on enemy type distribution
        let totalWeight = 0;
        let weightedModifier = 0;

        for (const enemyTypeData of enemyTypes) {
            const baseTypeModifier = enemyTypeModifiers[enemyTypeData.type] || 1.0;
            // Enhance modifier effects by 50% to make them more pronounced
            const enhancedTypeModifier = 1 + (baseTypeModifier - 1) * 1.5;
            totalWeight += enemyTypeData.weight;
            weightedModifier += enemyTypeData.weight * enhancedTypeModifier;
        }

        // Calculate the average modifier effect
        const averageModifier = totalWeight > 0 ? weightedModifier / totalWeight : 1.0;

        // Apply the modifier to spawn count
        const modifiedSpawnCount = Math.round(baseSpawnCount * averageModifier);

        console.log(`Applied enemy type modifiers to spawn count: ${baseSpawnCount} -> ${modifiedSpawnCount} (avg modifier: ${averageModifier.toFixed(2)})`);

        return Math.max(1, modifiedSpawnCount); // Ensure at least 1 enemy spawns
    }

    /**
     * Get enemy types for Space Invaders-style waves
     * @param {number} waveNumber - Wave number
     * @returns {Array} Array of enemy types with weights
     */
    getSpaceInvadersEnemyTypes(waveNumber) {
        // Space Invaders-style enemy distribution
        // Different rows have different enemy types (like original Space Invaders)
        const enemyTypes = [];

        // Row 0 (top): Tank enemies (Earth type - slow but tough)
        enemyTypes.push({ type: ENEMY_TYPES.EARTH, weight: 0.2, row: 0 });

        // Row 1: Shooter enemies (Fire and Crystal types - can fire)
        enemyTypes.push({ type: ENEMY_TYPES.FIRE, weight: 0.3, row: 1 });
        enemyTypes.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.2, row: 1 });

        // Row 2+: Basic enemies (Air, Water, Shadow - faster, more agile)
        enemyTypes.push({ type: ENEMY_TYPES.AIR, weight: 0.15, row: 2 });
        enemyTypes.push({ type: ENEMY_TYPES.WATER, weight: 0.1, row: 2 });
        enemyTypes.push({ type: ENEMY_TYPES.SHADOW, weight: 0.05, row: 2 });

        // Adjust weights based on wave number
        if (waveNumber >= 3) {
            // More shooters in later waves
            enemyTypes.find(e => e.type === ENEMY_TYPES.FIRE).weight += 0.1;
            enemyTypes.find(e => e.type === ENEMY_TYPES.CRYSTAL).weight += 0.1;
        }

        if (waveNumber >= 5) {
            // More agile enemies in later waves
            enemyTypes.find(e => e.type === ENEMY_TYPES.SHADOW).weight += 0.1;
            enemyTypes.find(e => e.type === ENEMY_TYPES.AIR).weight += 0.1;
        }

        return enemyTypes;
    }

    /**
     * Get Space Invaders-style wave pattern
     * @param {number} waveNumber - Wave number
     * @param {number} totalEnemies - Total enemies in wave
     * @param {number} cols - Number of columns in grid
     * @param {number} rows - Number of rows in grid
     * @returns {Array} Array of spawn patterns
     */
    getSpaceInvadersWavePattern(waveNumber, totalEnemies, cols, rows) {
        const patterns = [];

        // Single Space Invaders grid formation pattern
        patterns.push({
            formation: 'space_invaders_grid',
            enemyCount: totalEnemies,
            spawnDelay: 0, // All spawn at once
            gridCols: cols,
            gridRows: rows,
            // Enemy type assignment by row (Space Invaders style)
            rowTypes: this.getRowTypeAssignment(waveNumber, rows)
        });

        return patterns;
    }

    /**
     * Get enemy type assignment for each row (Space Invaders style)
     * @param {number} waveNumber - Wave number
     * @param {number} rows - Number of rows
     * @returns {Array} Array of enemy types for each row
     */
    getRowTypeAssignment(waveNumber, rows) {
        const rowTypes = [];

        for (let row = 0; row < rows; row++) {
            if (row === 0) {
                // Top row: Tank enemies (Earth type)
                rowTypes.push(ENEMY_TYPES.EARTH);
            } else if (row === 1 || (row % 2 === 1 && waveNumber >= 3)) {
                // Shooter rows: Alternate between Fire and Crystal
                rowTypes.push(row % 2 === 1 ? ENEMY_TYPES.FIRE : ENEMY_TYPES.CRYSTAL);
            } else {
                // Basic enemy rows: Mix of Air, Water, Shadow
                const basicTypes = [ENEMY_TYPES.AIR, ENEMY_TYPES.WATER];
                if (waveNumber >= 4) basicTypes.push(ENEMY_TYPES.SHADOW);
                rowTypes.push(basicTypes[row % basicTypes.length]);
            }
        }

        return rowTypes;
    }

    /**
     * Get enemy types for a specific wave with balanced mixtures (legacy method)
     * @param {number} waveNumber - Wave number
     * @returns {Array} Array of enemy types with weights
     */
    getWaveEnemyTypes(waveNumber) {
        const types = [];

        // Early waves (1-3) - Introduction to basic enemy types with balanced characteristics
        if (waveNumber <= 3) {
            // Wave 1: Mostly balanced Water enemies with some fast Air enemies
            if (waveNumber === 1) {
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.6 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.4 });   // Fast but low health
            }
            // Wave 2: Introduce Earth enemies (slow but tanky) alongside Water and Air
            else if (waveNumber === 2) {
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.4 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.3 });   // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.3 }); // Slow but high health
            }
            // Wave 3: Introduce Fire enemies (glass cannon - fast but fragile)
            else {
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.3 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.3 });   // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.2 }); // Slow but high health
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });  // Very fast but very fragile
            }
        }
        // Mid waves (4-7) - More variety with strategic combinations
        else if (waveNumber <= 7) {
            // Wave 4: Focus on tanky enemies with some fast harassers
            if (waveNumber === 4) {
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.4 }); // Slow but high health
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.3 }); // Balanced stats
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.3 });  // Fast but fragile
            }
            // Wave 5: Introduce Crystal enemies (special abilities) with balanced mix
            else if (waveNumber === 5) {
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.3 }); // Special abilities
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.3 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });   // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.2 }); // Slow but high health
            }
            // Wave 6: Fast-paced wave with Fire and Air enemies
            else if (waveNumber === 6) {
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.4 });  // Very fast but very fragile
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.3 });   // Fast but low health
                types.push({ type: ENEMY_TYPES.SHADOW, weight: 0.2 }); // Extremely fast but very low health
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.1 });  // Balanced stats
            }
            // Wave 7: Balanced mix with all basic types
            else {
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.25 }); // Balanced stats
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.25 });  // Very fast but very fragile
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });   // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.2 });  // Slow but high health
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.1 }); // Special abilities
            }
        }
        // Later waves (8+) - All types with strategic compositions
        else {
            // Wave 8: Tanky wave with Earth and Crystal enemies
            if (waveNumber === 8) {
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.4 });  // Very slow but very high health
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.3 }); // Special abilities
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.1 });   // Very fast but very fragile
            }
            // Wave 9: Shadow-focused wave with fast enemies
            else if (waveNumber === 9) {
                types.push({ type: ENEMY_TYPES.SHADOW, weight: 0.4 }); // Extremely fast but very low health
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.3 });   // Very fast but very fragile
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });    // Fast but low health
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.1 }); // Special abilities
            }
            // Wave 10: Balanced elite wave with all types
            else if (waveNumber === 10) {
                types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });   // Very fast but very fragile
                types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });    // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: 0.15 }); // Very slow but very high health
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.15 }); // Special abilities
                types.push({ type: ENEMY_TYPES.SHADOW, weight: 0.1 });  // Extremely fast but very low health
            }
            // Waves 11+: Progressive difficulty with increasing Shadow and Crystal presence
            else {
                const shadowWeight = Math.min(0.3, 0.1 + (waveNumber - 10) * 0.02);
                const crystalWeight = Math.min(0.25, 0.15 + (waveNumber - 10) * 0.015);
                const remainingWeight = 1 - shadowWeight - crystalWeight;
                
                types.push({ type: ENEMY_TYPES.SHADOW, weight: shadowWeight });     // Extremely fast but very low health
                types.push({ type: ENEMY_TYPES.CRYSTAL, weight: crystalWeight });   // Special abilities
                types.push({ type: ENEMY_TYPES.WATER, weight: remainingWeight * 0.3 });  // Balanced stats
                types.push({ type: ENEMY_TYPES.FIRE, weight: remainingWeight * 0.3 });   // Very fast but very fragile
                types.push({ type: ENEMY_TYPES.AIR, weight: remainingWeight * 0.2 });    // Fast but low health
                types.push({ type: ENEMY_TYPES.EARTH, weight: remainingWeight * 0.2 });  // Very slow but very high health
            }
        }

        return types;
    }

    /**
     * Generate spawn patterns for the wave with predefined patterns
     * @param {number} waveNumber - Wave number
     * @param {number} totalEnemies - Total enemies in wave
     * @returns {Array} Array of spawn patterns
     */
    generateSpawnPatterns(waveNumber, totalEnemies) {
        return this.getPredefinedWavePattern(waveNumber, totalEnemies);
    }

    /**
     * Get predefined movement pattern for specific wave
     * @param {number} waveNumber - Wave number
     * @param {number} totalEnemies - Total enemies in wave
     * @returns {Array} Array of spawn patterns
     */
    getPredefinedWavePattern(waveNumber, totalEnemies) {
        const patterns = [];

        // All waves use Space Invaders grid formation
        patterns.push({
            type: 'formation',
            count: totalEnemies,
            formation: 'space_invaders_grid',
            movementPattern: 'space_invaders',
            spacing: 800
        });

        return patterns;
    }

    /**
     * Spawn an enemy from the current wave configuration
     */
    spawnEnemyFromWave() {
        if (!this.waveConfig || this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies) {
            return;
        }

        // Check if this wave should have a boss and we haven't spawned one yet
        if (this.waveConfig.hasBoss && !this.bossEncounterActive && this.enemiesSpawnedInWave === Math.floor(this.waveConfig.totalEnemies * 0.7)) {
            this.spawnBossFromWave();
            return;
        }

        // Select enemy type based on wave configuration
        const enemyType = this.selectEnemyType(this.waveConfig.enemyTypes);

        // Select spawn pattern
        const patternData = this.selectSpawnPattern(this.waveConfig.spawnPatterns);
        const pattern = patternData.pattern;

        // Create spawn position
        const spawnPos = this.generateSpawnPosition(pattern);

        // Spawn the enemy
        const enemy = this.spawnEnemy(spawnPos.x, spawnPos.y, enemyType);

        if (enemy) {
            // Get type-specific movement pattern
            const typePattern = this.getTypeSpecificMovementPattern(enemyType);
            
            // Apply movement pattern with type-specific options
            const patternOptions = {
                amplitude: GameMath.random(30, 80),
                frequency: GameMath.random(1, 3)
            };

            // Add predefined pattern if specified
            if (pattern.predefinedPattern) {
                patternOptions.predefinedPattern = pattern.predefinedPattern;
            }

            // Use type-specific pattern if not in formation
            if (pattern.type !== 'formation') {
                enemy.setMovementPattern(typePattern.pattern, typePattern.options);
            } else {
                enemy.setMovementPattern(pattern.movementPattern, patternOptions);
            }

            // Handle formation spawning
            if (pattern.type === 'formation') {
                this.addEnemyToFormation(enemy, pattern);
            }

            this.enemiesSpawnedInWave++;
        }
    }
    
    /**
     * Spawn a boss from the current wave configuration
     */
    spawnBossFromWave() {
        if (!this.waveConfig || this.bossEncounterActive) {
            return;
        }

        // Select boss type based on wave configuration
        const enemyTypes = this.waveConfig.enemyTypes;
        const bossType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)].type;

        // Create spawn position (center top of screen)
        const spawnPos = new Vector2(this.canvasWidth / 2, 100);

        // Spawn the boss
        const boss = this.spawnBoss(spawnPos.x, spawnPos.y, bossType);

        if (boss) {
            // Set up boss callbacks
            boss.onWarpInitiated = (boss) => this.handleBossWarpInitiated(boss);
            boss.onWarpCompleted = (boss) => this.handleBossWarpCompleted(boss);
            boss.onAbilityUsed = (ability, playerPosition) => this.handleBossAbilityUsed(ability, playerPosition);
            boss.onDamageTaken = (damage, result) => this.handleBossDamageTaken(damage, result);

            this.enemiesSpawnedInWave++;
            console.log(`Boss "${boss.bossName}" spawned for wave ${this.currentWave}`);
        }
    }

    /**
     * Select enemy type based on weighted distribution
     * @param {Array} enemyTypes - Array of enemy types with weights
     * @returns {string} Selected enemy type
     */
    selectEnemyType(enemyTypes) {
        const random = Math.random();
        let cumulativeWeight = 0;

        for (const typeData of enemyTypes) {
            cumulativeWeight += typeData.weight;
            if (random <= cumulativeWeight) {
                return typeData.type;
            }
        }

        // Fallback to first type
        return enemyTypes[0].type;
    }

    /**
     * Select spawn pattern from available patterns
     * @param {Array} patterns - Available spawn patterns
     * @returns {object} Selected spawn pattern with index
     */
    selectSpawnPattern(patterns) {
        // Find patterns that haven't reached their spawn limit
        const availablePatterns = [];

        for (let i = 0; i < patterns.length; i++) {
            const pattern = patterns[i];
            const spawnedCount = this.patternSpawnCounts[i] || 0;

            if (spawnedCount < pattern.count) {
                availablePatterns.push({ pattern, index: i });
            }
        }

        if (availablePatterns.length === 0) {
            // All patterns exhausted, shouldn't happen but fallback
            console.warn('All spawn patterns exhausted but still trying to spawn enemies');
            return { pattern: patterns[0], index: 0 };
        }

        // Select randomly from available patterns
        const selected = availablePatterns[Math.floor(Math.random() * availablePatterns.length)];

        // Increment the spawn count for this pattern
        this.patternSpawnCounts[selected.index]++;

        return selected;
    }

    /**
     * Generate spawn position based on pattern
     * @param {object} pattern - Spawn pattern configuration
     * @returns {Vector2} Spawn position
     */
    generateSpawnPosition(pattern) {
        switch (pattern.type) {
            case 'linear':
                return new Vector2(
                    GameMath.random(50, this.canvasWidth - 50),
                    -30
                );

            case 'formation':
                // For formations, spawn at the top center with proper spacing
                // This ensures enemies start in a organized pattern
                const formation = this.getOrCreateFormation(pattern.formation);
                return new Vector2(
                    formation.center.x,
                    formation.center.y - 100
                );

            case 'scattered':
                return new Vector2(
                    GameMath.random(30, this.canvasWidth - 30),
                    GameMath.random(-50, -20)
                );

            case 'dive':
                // Spawn from sides for dive attacks
                const side = Math.random() < 0.5 ? 'left' : 'right';
                return new Vector2(
                    side === 'left' ? -30 : this.canvasWidth + 30,
                    GameMath.random(50, 150)
                );

            default:
                return new Vector2(
                    GameMath.random(50, this.canvasWidth - 50),
                    -30
                );
        }
    }

    /**
     * Get current level from level manager
     * @returns {number} Current level number
     */
    getCurrentLevel() {
        if (window.gameEngine && window.gameEngine.levelManager) {
            return window.gameEngine.levelManager.currentLevel;
        }
        return 1; // Default to level 1 if not available
    }

    /**
     * Spawn a new enemy at the specified position
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {string} type - Enemy type
     * @returns {Enemy|null} Spawned enemy or null if failed
     */
    spawnEnemy(x, y, type = ENEMY_TYPES.AIR) {
        // Get current level for scaling
        const currentLevel = this.getCurrentLevel();
        
        // Try to get enemy from pool first
        let enemy = this.getEnemyFromPool();

        if (!enemy) {
            // Create new enemy if pool is empty with level scaling
            enemy = new Enemy(x, y, type, currentLevel);
        } else {
            // Set type and level BEFORE reset so reset() can use the correct values
            enemy.type = type;
            enemy.level = currentLevel;
            enemy.reset();
            enemy.position.set(x, y);
        }

        // Environmental effects are now applied in Enemy constructor and reset method
        // No need to apply them here during spawning

        // Add enemy tag for consumable system
        enemy.addTag('enemy');

        // Add to active enemies
        this.activeEnemies.push(enemy);
        this.totalEnemiesSpawned++;

        // Register with game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.add(enemy);
        }

        console.log(`Spawned ${type} enemy at (${x}, ${y}). Active enemies: ${this.activeEnemies.length}`);
        return enemy;
    }

    /**
     * Apply level-based scaling to enemy stats
     * @param {Enemy} enemy - Enemy to scale
     * @param {number} level - Current level
     */
    applyLevelScaling(enemy, level) {
        // This method is now deprecated as scaling is handled in the Enemy constructor
        // Keeping it for compatibility but it no longer does anything
        console.warn(`applyLevelScaling is deprecated. Scaling is now handled in Enemy constructor.`);
        
        // Just update the debug info if needed
        if (!enemy.scalingInfo) {
            enemy.scalingInfo = {
                level: level,
                tier: enemy.tier,
                coefficient: enemy.difficultyCoefficient,
                isElite: enemy.isElite,
                message: "Scaling handled by constructor"
            };
        }
    }

    /**
     * Spawn a new boss at the specified position
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {string} type - Boss type
     * @returns {Boss|null} Spawned boss or null if failed
     */
    spawnBoss(x, y, type = ENEMY_TYPES.SHADOW) {
        // Try to get boss from pool first
        let boss = this.getBossFromPool();

        if (!boss) {
            // Create new boss if pool is empty
            boss = new Boss(x, y, type);
        } else {
            // Reset pooled boss
            boss.reset();
            boss.position.set(x, y);
            boss.type = type;
        }
        
        // Environmental effects are now applied in Enemy constructor and reset method
        // No need to apply them here during boss spawning
        
        // Add to active bosses
        this.activeBosses.push(boss);
        this.currentBoss = boss;
        this.bossEncounterActive = true;
        this.lastBossEncounterTime = Date.now();

        // Register with game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.add(boss);
        }

        console.log(`Spawned ${type} boss "${boss.bossName}" at (${x}, ${y}). Active bosses: ${this.activeBosses.length}`);
        return boss;
    }

    /**
     * Get enemy from object pool
     * @returns {Enemy|null} Pooled enemy or null if pool is empty
     */
    getEnemyFromPool() {
        return this.enemyPool.length > 0 ? this.enemyPool.pop() : null;
    }
    
    /**
     * Get boss from object pool
     * @returns {Boss|null} Pooled boss or null if pool is empty
     */
    getBossFromPool() {
        return this.bossPool.length > 0 ? this.bossPool.pop() : null;
    }

    /**
     * Return enemy to object pool
     * @param {Enemy} enemy - Enemy to return to pool
     */
    returnEnemyToPool(enemy) {
        if (this.enemyPool.length < this.maxEnemies) {
            enemy.reset();
            this.enemyPool.push(enemy);
        }

        // Remove from game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.remove(enemy);
        }
    }
    
    /**
     * Return boss to object pool
     * @param {Boss} boss - Boss to return to pool
     */
    returnBossToPool(boss) {
        if (this.bossPool.length < this.maxBosses) {
            boss.reset();
            this.bossPool.push(boss);
        }

        // Remove from game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.remove(boss);
        }
    }

    /**
     * Add enemy to formation
     * @param {Enemy} enemy - Enemy to add to formation
     * @param {object} pattern - Formation pattern
     */
    addEnemyToFormation(enemy, pattern) {
        const formation = this.getOrCreateFormation(pattern.formation);

        // Calculate formation position
        const formationIndex = formation.enemies.length;
        const offset = this.calculateFormationOffset(pattern.formation, formationIndex);

        // Set enemy movement pattern to formation
        enemy.setMovementPattern('formation');
        enemy.setFormationTarget(formation.center, offset);
        formation.enemies.push(enemy);

        console.log(`Added enemy to ${pattern.formation} formation. Formation size: ${formation.enemies.length}`);
    }

    /**
     * Get or create formation
     * @param {string} formationType - Type of formation
     * @returns {object} Formation object
     */
    getOrCreateFormation(formationType) {
        // Set max formation size based on type
        const maxSize = formationType === 'space_invaders_grid' ? 40 : 8;
        let formation = this.formations.find(f => f.type === formationType && f.enemies.length < maxSize);

        if (!formation) {
            formation = {
                type: formationType,
                center: new Vector2(this.canvasWidth / 2, 150), // Start a bit lower for Space Invaders
                enemies: [],
                movementTimer: 0
            };
            this.formations.push(formation);
        }

        return formation;
    }

    /**
     * Calculate formation offset for enemy position
     * @param {string} formationType - Type of formation
     * @param {number} index - Enemy index in formation
     * @returns {Vector2} Formation offset
     */
    calculateFormationOffset(formationType, index) {
        switch (formationType) {
            case 'space_invaders_grid':
                // Classic Space Invaders grid formation
                const cols = 8; // 8 enemies per row
                const gridRow = Math.floor(index / cols);
                const gridCol = index % cols;
                const spacing = 60; // Space between enemies
                const rowSpacing = 50; // Space between rows

                // Center the grid
                const gridWidth = (cols - 1) * spacing;
                const startX = -gridWidth / 2;

                return new Vector2(
                    startX + gridCol * spacing,
                    gridRow * rowSpacing
                );

            case 'line':
                // Improved spacing for line formation
                return new Vector2((index - 3.5) * 50, 0);

            case 'v-formation':
                // Improved V-formation with better spacing
                const side = index % 2 === 0 ? -1 : 1;
                const row = Math.floor(index / 2);
                return new Vector2(side * (row + 1) * 50, row * 40);

            case 'triangle':
                // Improved triangle formation with better spacing
                const triangleRow = Math.floor((-1 + Math.sqrt(1 + 8 * index)) / 2);
                const posInRow = index - (triangleRow * (triangleRow + 1)) / 2;
                const rowWidth = triangleRow + 1;
                return new Vector2((posInRow - rowWidth / 2 + 0.5) * 50, triangleRow * 40);

            case 'diamond':
                // Improved diamond formation with better spacing
                const diamondSize = 3;
                if (index < diamondSize) {
                    // Top part of diamond
                    return new Vector2((index - 1) * 60, -40);
                } else if (index < diamondSize * 2 - 1) {
                    // Middle part of diamond
                    const midIndex = index - diamondSize;
                    return new Vector2((midIndex - 1) * 80, 0);
                } else {
                    // Bottom part of diamond
                    const botIndex = index - (diamondSize * 2 - 1);
                    return new Vector2((botIndex - 1) * 60, 40);
                }

            case 'circle':
                // Improved circle formation with better spacing
                const angle = (index / 8) * Math.PI * 2;
                const radius = 80;
                return new Vector2(Math.cos(angle) * radius, Math.sin(angle) * radius);

            case 'wedge':
                // Improved wedge formation with better spacing
                const wedgeRow = Math.floor(index / 3);
                const wedgePos = index % 3;
                return new Vector2((wedgePos - 1) * (50 + wedgeRow * 15), wedgeRow * 40);

            default:
                // Default to line formation with improved spacing
                return new Vector2((index - 3.5) * 50, 0);
        }
    }

    /**
     * Update formations
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateFormations(deltaTime) {
        for (let i = this.formations.length - 1; i >= 0; i--) {
            const formation = this.formations[i];
            formation.movementTimer += deltaTime / 1000;

            // Remove destroyed enemies from formation
            formation.enemies = formation.enemies.filter(enemy => enemy.active && !enemy.destroyed);

            // Remove empty formations
            if (formation.enemies.length === 0) {
                this.formations.splice(i, 1);
                continue;
            }

            // Initialize formation movement properties if not set
            if (!formation.direction) {
                formation.direction = 1; // 1 for right, -1 for left
                // Use wave config properties for dynamic difficulty
                formation.moveSpeed = this.waveConfig?.formationSpeed || (40 + (this.currentWave * 6));
                formation.descentDistance = 0;
                formation.maxDescentDistance = 40;
                formation.horizontalDistance = 0;
                formation.maxHorizontalDistance = 150; // Reduced for more frequent direction changes
                formation.bobTimer = 0; // For bobbing animation
                formation.lastDirectionChange = 0; // Track direction changes
                formation.descentAmount = this.waveConfig?.descentAmount || (25 + (this.currentWave * 2));
            }

            // Space Invaders-style formation movement
            this.updateSpaceInvadersFormation(formation, deltaTime);

            // Update formation targets for all enemies in the formation
            for (let j = 0; j < formation.enemies.length; j++) {
                const enemy = formation.enemies[j];
                if (enemy.active && enemy.setFormationTarget) {
                    // Calculate offset with bobbing animation
                    const offset = this.calculateFormationOffset(formation.type, j);
                    const bobbingOffset = this.calculateBobbingOffset(formation, enemy, j);
                    const finalOffset = offset.add(bobbingOffset);

                    enemy.setFormationTarget(formation.center, finalOffset);

                    // Set enemy state for dive attack eligibility
                    enemy.formationState = 'formation';
                    enemy.formationIndex = j;
                }
            }
        }
    }

    /**
     * Update Space Invaders-style formation movement
     * @param {object} formation - Formation object
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateSpaceInvadersFormation(formation, deltaTime) {
        const dt = deltaTime / 1000;
        formation.bobTimer += dt;

        // Calculate formation bounds to check for edge collision
        const formationBounds = this.calculateFormationBounds(formation);
        const edgeMargin = 50;

        // Check if formation has hit the edges
        const hitLeftEdge = formationBounds.left <= edgeMargin;
        const hitRightEdge = formationBounds.right >= this.canvasWidth - edgeMargin;

        // Move horizontally
        formation.center.x += formation.direction * formation.moveSpeed * dt;
        formation.horizontalDistance += Math.abs(formation.direction * formation.moveSpeed * dt);

        // Check if formation should change direction and descend (Space Invaders style)
        if (hitLeftEdge || hitRightEdge || formation.horizontalDistance >= formation.maxHorizontalDistance) {
            // Change direction
            formation.direction *= -1;
            formation.horizontalDistance = 0;
            formation.lastDirectionChange = formation.movementTimer;

            // Play direction change sound effect
            this.playFormationDirectionChangeSound();

            // Descend (classic Space Invaders behavior)
            const descentAmount = formation.descentAmount || (25 + (this.currentWave * 2));
            formation.center.y += descentAmount;
            formation.descentDistance += descentAmount;

            // Clamp formation center to screen bounds
            formation.center.x = GameMath.clamp(formation.center.x, edgeMargin + 100, this.canvasWidth - edgeMargin - 100);

            // Increase speed slightly after each direction change (classic mechanic)
            formation.moveSpeed *= 1.02;
        }

        // Keep formation on screen
        formation.center.x = GameMath.clamp(formation.center.x, edgeMargin + 100, this.canvasWidth - edgeMargin - 100);
    }

    /**
     * Calculate formation bounds for edge detection
     * @param {object} formation - Formation object
     * @returns {object} Bounds object with left, right, top, bottom
     */
    calculateFormationBounds(formation) {
        if (formation.enemies.length === 0) {
            return { left: formation.center.x, right: formation.center.x, top: formation.center.y, bottom: formation.center.y };
        }

        let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;

        for (let i = 0; i < formation.enemies.length; i++) {
            const offset = this.calculateFormationOffset(formation.type, i);
            const enemyX = formation.center.x + offset.x;
            const enemyY = formation.center.y + offset.y;

            minX = Math.min(minX, enemyX - 20); // Account for enemy size
            maxX = Math.max(maxX, enemyX + 20);
            minY = Math.min(minY, enemyY - 15);
            maxY = Math.max(maxY, enemyY + 15);
        }

        return { left: minX, right: maxX, top: minY, bottom: maxY };
    }

    /**
     * Calculate bobbing offset for individual enemy animation
     * @param {object} formation - Formation object
     * @param {Enemy} enemy - Enemy object
     * @param {number} index - Enemy index in formation
     * @returns {Vector2} Bobbing offset
     */
    calculateBobbingOffset(formation, enemy, index) {
        // Each enemy has a unique bobbing phase and amplitude
        const bobPhase = (index * 0.5) + (formation.bobTimer * 2); // Stagger the bobbing
        const bobAmplitude = 4 + (index % 3) * 2; // Vary amplitude slightly

        const bobY = Math.sin(bobPhase) * bobAmplitude;

        return new Vector2(0, bobY);
    }

    /**
     * Update dive attack system (Space Invaders style)
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateDiveAttacks(deltaTime, playerPosition) {
        if (!playerPosition) return;

        const dt = deltaTime / 1000;
        this.nextDiveTime -= dt;

        // Check if it's time for a dive attack
        if (this.nextDiveTime <= 0) {
            this.triggerDiveAttack(playerPosition);

            // Set next dive time using wave config or fallback
            const baseCooldown = this.waveConfig?.diveFrequency || Math.max(0.8, this.diveAttackCooldown - (this.currentWave * 0.1));
            this.nextDiveTime = baseCooldown + (Math.random() * 2.0);
        }

        // Update diving enemies
        this.updateDivingEnemies(deltaTime, playerPosition);
    }

    /**
     * Trigger a dive attack by selecting a random enemy from formation
     * @param {Vector2} playerPosition - Player's current position
     */
    triggerDiveAttack(playerPosition) {
        // Find all enemies in formation that can dive
        const candidates = [];

        for (const formation of this.formations) {
            for (const enemy of formation.enemies) {
                if (enemy.active && !enemy.destroyed &&
                    enemy.formationState === 'formation' &&
                    !this.divingEnemies.has(enemy)) {
                    candidates.push(enemy);
                }
            }
        }

        // Select a random candidate
        if (candidates.length > 0) {
            const selectedEnemy = candidates[Math.floor(Math.random() * candidates.length)];
            this.startEnemyDive(selectedEnemy, playerPosition);
        }
    }

    /**
     * Start dive attack for a specific enemy
     * @param {Enemy} enemy - Enemy to start diving
     * @param {Vector2} playerPosition - Player's current position
     */
    startEnemyDive(enemy, playerPosition) {
        // Set enemy to dive state
        enemy.formationState = 'dive';
        enemy.diveStartTime = 0;
        enemy.diveReturnTime = 3.0 + (Math.random() * 2.0); // 3-5 seconds dive duration
        enemy.originalFormationCenter = enemy.formationCenter ? enemy.formationCenter.clone() : null;
        enemy.originalFormationOffset = enemy.formationOffset ? enemy.formationOffset.clone() : null;

        // Set initial dive velocity toward player
        const direction = enemy.directionTo(playerPosition);
        const diveSpeed = 220 + (this.currentWave * 20); // Increase speed with wave
        enemy.velocity = direction.multiply(diveSpeed);

        // Add to diving enemies set
        this.divingEnemies.add(enemy);

        console.log(`Enemy starting dive attack toward player`);

        // Visual and audio effects for dive attack
        this.createDiveAttackEffect(enemy.position);
        this.playDiveAttackSound();
    }

    /**
     * Update all diving enemies
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateDivingEnemies(deltaTime, playerPosition) {
        const dt = deltaTime / 1000;
        const enemiesToReturn = [];

        for (const enemy of this.divingEnemies) {
            if (!enemy.active || enemy.destroyed) {
                enemiesToReturn.push(enemy);
                continue;
            }

            enemy.diveStartTime += dt;

            // Homing behavior toward player
            if (enemy.formationState === 'dive') {
                const direction = enemy.directionTo(playerPosition);
                const homingStrength = 180 * dt; // How quickly they adjust course

                // Gradually adjust velocity toward player
                enemy.velocity.x += direction.x * homingStrength;
                enemy.velocity.y += direction.y * homingStrength;

                // Limit maximum dive speed
                const maxSpeed = 300 + (this.currentWave * 30);
                if (enemy.velocity.magnitude() > maxSpeed) {
                    enemy.velocity = enemy.velocity.normalize().multiply(maxSpeed);
                }
            }

            // Check if enemy should return to formation
            const shouldReturn = enemy.diveStartTime >= enemy.diveReturnTime ||
                                enemy.position.y > this.canvasHeight + 40 ||
                                enemy.position.x < -40 ||
                                enemy.position.x > this.canvasWidth + 40;

            if (shouldReturn) {
                enemiesToReturn.push(enemy);
            }
        }

        // Return enemies to formation
        for (const enemy of enemiesToReturn) {
            this.returnEnemyToFormation(enemy);
        }
    }

    /**
     * Return diving enemy to formation
     * @param {Enemy} enemy - Enemy to return to formation
     */
    returnEnemyToFormation(enemy) {
        // Remove from diving enemies
        this.divingEnemies.delete(enemy);

        // Reset enemy state
        enemy.formationState = 'formation';
        enemy.diveStartTime = 0;

        // If enemy went off screen, reposition it back in formation
        if (enemy.position.y > this.canvasHeight + 40 ||
            enemy.position.x < -40 ||
            enemy.position.x > this.canvasWidth + 40) {

            // Find a formation to rejoin or create new position
            if (enemy.originalFormationCenter && enemy.originalFormationOffset) {
                const newY = Math.max(80, enemy.originalFormationCenter.y - 60); // Above formation
                enemy.position.set(
                    enemy.originalFormationCenter.x + enemy.originalFormationOffset.x,
                    newY
                );
            } else {
                // Default repositioning
                enemy.position.set(
                    GameMath.random(100, this.canvasWidth - 100),
                    80
                );
            }
        }

        // Reset movement pattern to formation
        enemy.setMovementPattern('formation');

        console.log(`Enemy returned to formation`);

        // Visual effect for enemy returning to formation
        this.createReturnToFormationEffect(enemy.position);
    }

    /**
     * Create visual effect for formation spawn
     * @param {Vector2} center - Formation center position
     * @param {number} cols - Number of columns
     * @param {number} rows - Number of rows
     */
    createFormationSpawnEffect(center, cols, rows) {
        // Create particle effects for formation spawn
        if (this.effectsManager) {
            // Grid spawn effect - particles appear at each enemy position
            const spacing = 60;
            const rowSpacing = 50;
            const gridWidth = (cols - 1) * spacing;
            const startX = center.x - gridWidth / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    const x = startX + col * spacing;
                    const y = center.y + row * rowSpacing;

                    this.effectsManager.createEffect('spawn_flash', new Vector2(x, y), {
                        color: '#00FFFF',
                        duration: 500,
                        size: 20
                    });
                }
            }
        }

        // Play formation spawn sound
        this.playFormationSpawnSound();
    }

    /**
     * Create visual effect for dive attack
     * @param {Vector2} position - Enemy position
     */
    createDiveAttackEffect(position) {
        if (this.effectsManager) {
            // Create warning effect before dive
            this.effectsManager.createEffect('warning_flash', position.clone(), {
                color: '#FF4444',
                duration: 300,
                size: 30,
                pulseRate: 10
            });

            // Create trail effect for diving enemy
            this.effectsManager.createEffect('dive_trail', position.clone(), {
                color: '#FFAA00',
                duration: 2000,
                followTarget: true
            });
        }
    }

    /**
     * Create visual effect for enemy returning to formation
     * @param {Vector2} position - Enemy position
     */
    createReturnToFormationEffect(position) {
        if (this.effectsManager) {
            this.effectsManager.createEffect('return_flash', position.clone(), {
                color: '#00FF00',
                duration: 400,
                size: 15
            });
        }
    }

    /**
     * Play formation spawn sound effect
     */
    playFormationSpawnSound() {
        if (this.audioManager) {
            this.audioManager.playSound('formation_spawn', {
                volume: 0.6,
                pitch: 1.0
            });
        }
    }

    /**
     * Play dive attack sound effect
     */
    playDiveAttackSound() {
        if (this.audioManager) {
            this.audioManager.playSound('dive_attack', {
                volume: 0.7,
                pitch: 1.2
            });
        }
    }

    /**
     * Play formation direction change sound effect
     */
    playFormationDirectionChangeSound() {
        if (this.audioManager) {
            this.audioManager.playSound('formation_turn', {
                volume: 0.5,
                pitch: 0.8 + (this.currentWave * 0.1) // Higher pitch with higher waves
            });
        }
    }

    /**
     * Process formation spawn queue
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    processFormationSpawnQueue(deltaTime) {
        // Implementation for queued formation spawning
        // This can be used for more complex formation spawning patterns
    }

    /**
     * Check if current wave is complete
     */
    checkWaveCompletion() {
        if (this.waveInProgress && this.waveConfig) {
            const totalProcessed = this.enemiesKilledInWave + this.enemiesEscapedInWave;
            const allEnemiesSpawned = this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies;
            const allEnemiesProcessed = totalProcessed >= this.waveConfig.totalEnemies;
            const noActiveEnemies = this.activeEnemies.length === 0;

            if (allEnemiesSpawned && (allEnemiesProcessed || noActiveEnemies)) {
                this.completeWave();
            }
        }
    }

    /**
     * Complete the current wave
     */
    completeWave() {
        this.waveInProgress = false;

        console.log(`Wave ${this.currentWave} completed! Enemies killed: ${this.enemiesKilledInWave}/${this.waveConfig.totalEnemies}, Enemies escaped: ${this.enemiesEscapedInWave}/${this.waveConfig.totalEnemies}`);

        // Calculate wave completion bonus
        const completionBonus = this.calculateWaveBonus();
        this.totalScore += completionBonus;

        // Clear formations
        this.formations = [];

        // Trigger wave completion event
        this.onWaveComplete(this.currentWave, completionBonus);
    }

    /**
     * Calculate wave completion bonus
     * @returns {number} Bonus score
     */
    calculateWaveBonus() {
        const baseBonus = 100;
        const waveMultiplier = this.currentWave;
        const completionRatio = this.enemiesKilledInWave / this.waveConfig.totalEnemies;

        return Math.floor(baseBonus * waveMultiplier * completionRatio);
    }

    /**
     * Wave completion callback (override in game)
     * @param {number} waveNumber - Completed wave number
     * @param {number} bonus - Completion bonus
     */
    onWaveComplete(waveNumber, bonus) {
        // Override this method in the game to handle wave completion
        console.log(`Wave ${waveNumber} complete with bonus: ${bonus}`);
    }

    /**
     * Enemy escaped callback (override in game)
     * @param {object} enemy - Enemy that escaped
     */
    onEnemyEscaped(enemy) {
        // Override this method in the game to handle enemy escapes
        console.log(`Enemy ${enemy.id} escaped`);
    }

    /**
     * Render all active enemies and projectiles
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        // Render enemies
        for (const enemy of this.activeEnemies) {
            if (enemy.visible) {
                enemy.render(ctx, interpolation);
            }
        }
        
        // Render bosses
        for (const boss of this.activeBosses) {
            if (boss.visible) {
                boss.render(ctx, interpolation);
            }
        }

        // Render enemy projectiles
        if (this.projectileSystem) {
            this.projectileSystem.render(ctx, interpolation);
        }

        // Render debug information
        if (window.DEBUG_MODE) {
            this.renderDebugInfo(ctx);
        }
    }

    /**
     * Render debug information
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderDebugInfo(ctx) {
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const debugInfo = [
            `Wave: ${this.currentWave}`,
            `Active Enemies: ${this.activeEnemies.length}`,
            `Active Bosses: ${this.activeBosses.length}`,
            `Boss Encounter: ${this.bossEncounterActive ? 'Active' : 'Inactive'}`,
            `Spawned: ${this.enemiesSpawnedInWave}/${this.waveConfig?.totalEnemies || 0}`,
            `Killed: ${this.enemiesKilledInWave}`,
            `Total Score: ${this.totalScore}`,
            `Formations: ${this.formations.length}`
        ];

        for (let i = 0; i < debugInfo.length; i++) {
            ctx.fillText(debugInfo[i], 10, 20 + i * 15);
        }

        // Draw formation centers
        ctx.strokeStyle = '#FFFF00';
        ctx.lineWidth = 2;
        for (const formation of this.formations) {
            ctx.beginPath();
            ctx.arc(formation.center.x, formation.center.y, 5, 0, Math.PI * 2);
            ctx.stroke();
        }
        
        // Highlight boss positions
        ctx.strokeStyle = '#FF00FF';
        ctx.lineWidth = 3;
        for (const boss of this.activeBosses) {
            ctx.beginPath();
            ctx.arc(boss.position.x, boss.position.y, boss.collisionRadius + 10, 0, Math.PI * 2);
            ctx.stroke();
        }
    }

    /**
     * Check collision between player and enemies
     * @param {PlayerShip} player - Player ship
     * @returns {Array} Array of colliding enemies
     */
    checkPlayerCollisions(player) {
        const collisions = [];

        if (!player.active || player.isInvulnerable) {
            return collisions;
        }

        for (const enemy of this.activeEnemies) {
            if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(player)) {
                collisions.push(enemy);
            }
        }

        return collisions;
    }

    /**
     * Check collision between projectiles and enemies
     * @param {Array} projectiles - Array of projectiles
     * @returns {Array} Array of collision results
     */
    checkProjectileCollisions(projectiles) {
        const collisions = [];

        for (const projectile of projectiles) {
            if (!projectile.active || projectile.hasTag('enemyProjectile')) {
                continue; // Skip enemy projectiles
            }

            for (const enemy of this.activeEnemies) {
                if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(projectile)) {
                    // Calculate effective damage based on weapon variant vs enemy type
                    let effectiveDamage = projectile.damage || 25;
                    if (projectile.calculateEffectiveDamage && enemy.type) {
                        effectiveDamage = projectile.calculateEffectiveDamage(enemy.type);
                    }

                    collisions.push({
                        projectile: projectile,
                        enemy: enemy,
                        damage: effectiveDamage
                    });
                    break; // Projectile can only hit one enemy
                }
            }
        }

        return collisions;
    }

    /**
     * Handle collision between player and enemy
     * @param {PlayerShip} player - Player ship
     * @param {Enemy} enemy - Colliding enemy
     */
    handlePlayerEnemyCollision(player, enemy) {
        // Player takes damage based on enemy's type-specific damage
        const damageResult = player.takeDamage(enemy.damage);

        // Enemy is destroyed on collision
        enemy.takeDamage(enemy.health);

        console.log(`Player-Enemy collision: Player took ${damageResult.damageTaken} damage from ${enemy.type} enemy, Enemy destroyed`);

        return {
            playerDamage: damageResult.damageTaken,
            enemyDestroyed: true,
            scoreGained: enemy.scoreValue
        };
    }

    /**
     * Handle collision between projectile and enemy
     * @param {Projectile} projectile - Projectile
     * @param {Enemy} enemy - Enemy
     * @param {number} damage - Damage amount
     */
    handleProjectileEnemyCollision(projectile, enemy, damage) {
        // Enemy takes damage
        const damageResult = enemy.takeDamage(damage);

        // Destroy projectile
        projectile.destroy();

        console.log(`Projectile-Enemy collision: Enemy took ${damageResult.damageTaken} damage`);

        // Check if enemy should drop a power-up (1 in 10 chance when destroyed)
        let powerUpDropType = null;
        if (damageResult.destroyed) {
            powerUpDropType = this.shouldDropPowerUp();
        }

        return {
            enemyDestroyed: damageResult.destroyed,
            scoreGained: damageResult.scoreValue,
            damageDealt: damageResult.damageTaken,
            powerUpDropType: powerUpDropType,
            enemyPosition: damageResult.destroyed ? enemy.position.clone() : null
        };
    }

    /**
     * Determine if an enemy should drop a power-up (1 in 10 chance)
     * @returns {string|null} Power-up type to drop, or null if no drop
     */
    shouldDropPowerUp() {
        // 1 in 10 chance to drop a power-up
        if (Math.random() < 0.1) {
            // Randomly choose between rapid-fire and shield power-ups
            const powerUpTypes = ['RAPID_FIRE', 'SHIELD'];
            const randomIndex = Math.floor(Math.random() * powerUpTypes.length);
            return powerUpTypes[randomIndex];
        }
        return null;
    }

    /**
     * Apply environmental effects to enemy
     * @param {Enemy} enemy - Enemy to apply effects to
     */
    applyEnvironmentalEffects(enemy) {
        if (!this.environmentalEffects || typeof this.environmentalEffects.applyEnvironmentEffects !== 'function') return;
        
        const enemyStats = {
            type: enemy.type,
            speed: enemy.speed,
            health: enemy.maxHealth,
            projectileSpeed: 1.0 // Assuming a base projectile speed
        };
        
        const modifiedStats = this.environmentalEffects.applyEnvironmentEffects(enemyStats);
        
        enemy.currentSpeed = modifiedStats.speed;
        // DO NOT reset health - this was causing enemies to heal every frame!
        // enemy.health = modifiedStats.health;
        // enemy.projectileSpeed = modifiedStats.projectileSpeed; // If enemies have projectiles
    }
    
    /**
     * Apply environmental effects to enemy from level configuration
     * @param {Enemy} enemy - Enemy to apply effects to
     */
    applyEnvironmentalEffectsFromLevelConfig(enemy) {
        if (!window.gameEngine || !window.gameEngine.levelManager ||
            !window.gameEngine.levelManager.levelConfig ||
            !window.gameEngine.levelManager.levelConfig.environmentData) return;
        
        const environmentData = window.gameEngine.levelManager.levelConfig.environmentData;
        const gameplayModifiers = environmentData.gameplayModifiers;
        
        if (!gameplayModifiers) return;
        
        // Get enemy type modifier
        const enemyType = enemy.type;
        const baseTypeModifier = gameplayModifiers.enemyTypeModifiers?.[enemyType] ?? 1.0;

        // Enhance modifier effects by 50% to make them more pronounced
        const enhancedTypeModifier = 1 + (baseTypeModifier - 1) * 1.5;
        const enhancedSpeedMultiplier = 1 + ((gameplayModifiers.enemySpeedMultiplier ?? 1.0) - 1) * 1.5;

        // Apply enhanced modifiers
        enemy.currentSpeed = enemy.speed * enhancedSpeedMultiplier * enhancedTypeModifier;
        // DO NOT reset health - this was causing enemies to heal every frame!
    }

    /**
     * Calculate environmental effectiveness for enemy type
     * @param {string} enemyType - Enemy type
     * @param {string} environment - Current environment
     * @returns {number} Effectiveness multiplier
     */
    calculateEnvironmentalEffectiveness(enemyType, environment) {
        const effectivenessMap = this.environmentalEffects[environment];
        return effectivenessMap ? (effectivenessMap[enemyType] || 1.0) : 1.0;
    }

    /**
     * Set current environment and update all enemies
     * @param {string} environment - New environment type
     * @param {object} effects - Environmental effects configuration
     */
    setEnvironment(environment) {
        this.currentEnvironment = environment.getEnvironmentType();
        this.environmentalEffects = environment;

        // Update all active enemies
        for (const enemy of this.activeEnemies) {
            this.applyEnvironmentalEffects(enemy);
        }

        console.log(`Environment changed to: ${this.currentEnvironment}`);
    }

    /**
     * Get type-specific movement pattern based on enemy characteristics
     * @param {string} enemyType - Type of enemy
     * @returns {object} Movement pattern configuration with pattern name and options
     */
    getTypeSpecificMovementPattern(enemyType) {
        switch (enemyType) {
            case ENEMY_TYPES.WATER:
                // Water enemies: Fluid movement with occasional direction changes
                return {
                    pattern: 'sine',
                    options: {
                        amplitude: 60,
                        frequency: 1.5,
                        changeInterval: 3000 // Change direction every 3 seconds
                    }
                };
                
            case ENEMY_TYPES.FIRE:
                // Fire enemies: Aggressive, direct movement with occasional dashes
                return {
                    pattern: 'player_follow',
                    options: {
                        aggression: 0.7, // How directly they follow the player
                        dashCooldown: 4000, // Dash every 4 seconds
                        dashDuration: 800 // Dash for 0.8 seconds
                    }
                };
                
            case ENEMY_TYPES.AIR:
                // Air enemies: Evasive, zigzag patterns
                return {
                    pattern: 'sine',
                    options: {
                        amplitude: 100,
                        frequency: 2.5,
                        evasionMode: true // Enables special evasion behavior
                    }
                };
                
            case ENEMY_TYPES.EARTH:
                // Earth enemies: Slow, steady movement with occasional pauses
                return {
                    pattern: 'straight',
                    options: {
                        pauseInterval: 5000, // Pause every 5 seconds
                        pauseDuration: 1500, // Pause for 1.5 seconds
                        slowSteady: true // Movement modifier
                    }
                };
                
            case ENEMY_TYPES.CRYSTAL:
                // Crystal enemies: Geometric patterns with periodic pulses
                return {
                    pattern: 'sine',
                    options: {
                        amplitude: 80,
                        frequency: 1.0,
                        geometric: true,
                        pulseInterval: 6000 // Pulse every 6 seconds
                    }
                };
                
            case ENEMY_TYPES.SHADOW:
                // Shadow enemies: Teleportation and stealthy movement
                return {
                    pattern: 'player_follow',
                    options: {
                        stealthMode: true,
                        teleportInterval: 7000, // Teleport every 7 seconds
                        indirectApproach: true // Move indirectly toward player
                    }
                };
                
            default:
                // Default movement pattern
                return {
                    pattern: 'straight',
                    options: {}
                };
        }
    }

    /**
     * Get default environmental effects
     * @returns {object} Default environmental effects configuration
     */
    getDefaultEnvironmentalEffects() {
        return {
            space: {
                [ENEMY_TYPES.AIR]: 1.2,
                [ENEMY_TYPES.WATER]: 0.8,
                [ENEMY_TYPES.FIRE]: 1.0,
                [ENEMY_TYPES.EARTH]: 0.9,
                [ENEMY_TYPES.CRYSTAL]: 1.1,
                [ENEMY_TYPES.SHADOW]: 1.0
            },
            underwater: {
                [ENEMY_TYPES.AIR]: 0.6,
                [ENEMY_TYPES.WATER]: 1.5,
                [ENEMY_TYPES.FIRE]: 0.3,
                [ENEMY_TYPES.EARTH]: 0.8,
                [ENEMY_TYPES.CRYSTAL]: 1.0,
                [ENEMY_TYPES.SHADOW]: 0.9
            },
            volcanic: {
                [ENEMY_TYPES.AIR]: 0.8,
                [ENEMY_TYPES.WATER]: 0.4,
                [ENEMY_TYPES.FIRE]: 1.6,
                [ENEMY_TYPES.EARTH]: 1.3,
                [ENEMY_TYPES.CRYSTAL]: 0.9,
                [ENEMY_TYPES.SHADOW]: 0.7
            },
            crystal: {
                [ENEMY_TYPES.AIR]: 1.0,
                [ENEMY_TYPES.WATER]: 0.9,
                [ENEMY_TYPES.FIRE]: 0.8,
                [ENEMY_TYPES.EARTH]: 1.1,
                [ENEMY_TYPES.CRYSTAL]: 1.8,
                [ENEMY_TYPES.SHADOW]: 1.2
            },
            forest: {
                [ENEMY_TYPES.AIR]: 0.7,
                [ENEMY_TYPES.WATER]: 1.1,
                [ENEMY_TYPES.FIRE]: 0.6,
                [ENEMY_TYPES.EARTH]: 1.4,
                [ENEMY_TYPES.CRYSTAL]: 0.8,
                [ENEMY_TYPES.SHADOW]: 1.3
            }
        };
    }

    /**
     * Update enemy attacks and projectile firing (Space Invaders style)
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateEnemyAttacks(deltaTime, playerPosition) {
        if (!playerPosition) return;

        for (const enemy of this.activeEnemies) {
            if (enemy.active && !enemy.isDestroyed) {
                // Update enemy firing cooldown
                this.updateEnemyFiring(enemy, deltaTime, playerPosition);
            }
        }
    }

    /**
     * Update individual enemy firing mechanics (Space Invaders style)
     * @param {Enemy} enemy - Enemy to update
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateEnemyFiring(enemy, deltaTime, playerPosition) {
        // Initialize firing properties if not set
        if (enemy.fireCooldown === undefined) {
            enemy.fireCooldown = this.getEnemyFireCooldown(enemy.type);
            enemy.lastFireTime = 0;
        }

        // Update fire cooldown
        enemy.fireCooldown -= deltaTime;

        // Check if enemy can fire (Space Invaders style conditions)
        const canFire = this.canEnemyFire(enemy, playerPosition);

        if (canFire && enemy.fireCooldown <= 0) {
            this.fireEnemyProjectile(enemy, playerPosition);

            // Reset cooldown with some randomness
            enemy.fireCooldown = this.getEnemyFireCooldown(enemy.type) + (Math.random() * 500);
        }
    }

    /**
     * Check if enemy can fire (Space Invaders style conditions)
     * @param {Enemy} enemy - Enemy to check
     * @param {Vector2} playerPosition - Player's position
     * @returns {boolean} Whether enemy can fire
     */
    canEnemyFire(enemy, playerPosition) {
        // Check if enemy firing is disabled by consumable effects
        const now = Date.now();
        if (enemy.empDisabled && enemy.empDisableEndTime && now < enemy.empDisableEndTime) {
            return false;
        } else if (enemy.empDisabled && enemy.empDisableEndTime && now >= enemy.empDisableEndTime) {
            // Re-enable firing if EMP effect has expired
            enemy.empDisabled = false;
            enemy.empDisableEndTime = null;
        }

        if (enemy.timeDilationDisabled && enemy.timeDilationEndTime && now < enemy.timeDilationEndTime) {
            return false;
        } else if (enemy.timeDilationDisabled && enemy.timeDilationEndTime && now >= enemy.timeDilationEndTime) {
            // Re-enable firing if Time Dilation effect has expired
            enemy.timeDilationDisabled = false;
            enemy.timeDilationEndTime = null;
        }

        // All enemy types can fire, but with different characteristics
        // Some types fire more frequently than others
        const firingChance = this.getEnemyFiringChance(enemy.type);
        if (Math.random() > firingChance) {
            return false;
        }

        // Don't fire if diving (they're focused on attacking directly)
        if (enemy.formationState === 'dive') {
            return false;
        }

        // Check if enemy is in a good position to fire (not too close to screen edges)
        if (enemy.position.x < 50 || enemy.position.x > this.canvasWidth - 50) {
            return false;
        }

        // Check if player is roughly below the enemy (Space Invaders style)
        const playerBelowEnemy = playerPosition.y > enemy.position.y;
        const horizontalDistance = Math.abs(playerPosition.x - enemy.position.x);
        const inFiringRange = horizontalDistance < 200; // Within reasonable firing range

        return playerBelowEnemy && inFiringRange;
    }

    /**
     * Fire projectile from enemy with type-specific patterns
     * @param {Enemy} enemy - Enemy firing the projectile
     * @param {Vector2} playerPosition - Player's position
     */
    fireEnemyProjectile(enemy, playerPosition) {
        // Use type-specific firing patterns
        switch (enemy.type) {
            case ENEMY_TYPES.FIRE:
                this.fireFireEnemyPattern(enemy, playerPosition);
                break;
            case ENEMY_TYPES.WATER:
                this.fireWaterEnemyPattern(enemy, playerPosition);
                break;
            case ENEMY_TYPES.AIR:
                this.fireAirEnemyPattern(enemy, playerPosition);
                break;
            case ENEMY_TYPES.EARTH:
                this.fireEarthEnemyPattern(enemy, playerPosition);
                break;
            case ENEMY_TYPES.CRYSTAL:
                this.fireCrystalEnemyPattern(enemy, playerPosition);
                break;
            case ENEMY_TYPES.SHADOW:
                this.fireShadowEnemyPattern(enemy, playerPosition);
                break;
            default:
                this.fireDefaultEnemyPattern(enemy, playerPosition);
                break;
        }
    }

    /**
     * Fire spread shot pattern from enemy
     * @param {Enemy} enemy - Enemy firing
     * @param {Vector2} playerPosition - Player's position
     */
    fireEnemySpreadShot(enemy, playerPosition) {
        // Check if projectile system is available
        if (!this.projectileSystem) {
            return;
        }

        const baseDirection = enemy.directionTo(playerPosition);
        const spreadAngle = Math.PI / 8; // 22.5 degrees spread

        // Fire left and right spread shots
        const leftDirection = baseDirection.rotate(-spreadAngle);
        const rightDirection = baseDirection.rotate(spreadAngle);

        this.projectileSystem.fireProjectile(
            enemy.position.clone(),
            leftDirection,
            enemy.type,
            { damage: this.getEnemyProjectileDamage(enemy.type) * 0.8 } // Slightly less damage
        );

        this.projectileSystem.fireProjectile(
            enemy.position.clone(),
            rightDirection,
            enemy.type,
            { damage: this.getEnemyProjectileDamage(enemy.type) * 0.8 }
        );
    }

    /**
     * Fire pattern for Fire enemies - Burst shots with aggressive targeting
     * @param {Enemy} enemy - Fire enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireFireEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Fire a burst of 2-3 shots with slight spread
        const burstCount = Math.random() < 0.3 ? 3 : 2;
        const spreadAngle = Math.PI / 12; // 15 degrees

        for (let i = 0; i < burstCount; i++) {
            const angle = (i - (burstCount - 1) / 2) * spreadAngle;
            const direction = baseDirection.rotate(angle);

            setTimeout(() => {
                this.projectileSystem.fireProjectile(
                    enemy.position.clone(),
                    direction,
                    enemy.type,
                    { damage: this.getEnemyProjectileDamage(enemy.type) }
                );
            }, i * 100); // 100ms delay between shots
        }
    }

    /**
     * Fire pattern for Water enemies - Wave-like spread pattern
     * @param {Enemy} enemy - Water enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireWaterEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Fire in a wave pattern - 3 shots with increasing spread
        const waveCount = 3;
        const maxSpread = Math.PI / 6; // 30 degrees max spread

        for (let i = 0; i < waveCount; i++) {
            const spreadFactor = (i / (waveCount - 1)) - 0.5; // -0.5 to 0.5
            const angle = spreadFactor * maxSpread;
            const direction = baseDirection.rotate(angle);

            setTimeout(() => {
                this.projectileSystem.fireProjectile(
                    enemy.position.clone(),
                    direction,
                    enemy.type,
                    { damage: this.getEnemyProjectileDamage(enemy.type) }
                );
            }, i * 150); // 150ms delay between shots
        }
    }

    /**
     * Fire pattern for Air enemies - Rapid, less accurate shots
     * @param {Enemy} enemy - Air enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireAirEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Add more inaccuracy for air enemies (they're fast but less precise)
        const inaccuracy = (Math.random() - 0.5) * 0.6;
        const direction = new Vector2(
            baseDirection.x + inaccuracy,
            baseDirection.y + inaccuracy * 0.3
        ).normalize();

        this.projectileSystem.fireProjectile(
            enemy.position.clone(),
            direction,
            enemy.type,
            { damage: this.getEnemyProjectileDamage(enemy.type) }
        );
    }

    /**
     * Fire pattern for Earth enemies - Slow, heavy, accurate shots
     * @param {Enemy} enemy - Earth enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireEarthEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Earth enemies are very accurate but slow
        const direction = baseDirection; // No inaccuracy

        this.projectileSystem.fireProjectile(
            enemy.position.clone(),
            direction,
            enemy.type,
            { damage: this.getEnemyProjectileDamage(enemy.type) }
        );
    }

    /**
     * Fire pattern for Crystal enemies - Geometric patterns with precision
     * @param {Enemy} enemy - Crystal enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireCrystalEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Crystal enemies fire in geometric patterns
        if (Math.random() < 0.4) {
            // 40% chance for triangle pattern
            const angles = [-Math.PI / 8, 0, Math.PI / 8]; // -22.5°, 0°, 22.5°

            angles.forEach((angle, index) => {
                const direction = baseDirection.rotate(angle);
                setTimeout(() => {
                    this.projectileSystem.fireProjectile(
                        enemy.position.clone(),
                        direction,
                        enemy.type,
                        { damage: this.getEnemyProjectileDamage(enemy.type) }
                    );
                }, index * 80); // 80ms delay between shots
            });
        } else {
            // Single precise shot
            this.projectileSystem.fireProjectile(
                enemy.position.clone(),
                baseDirection,
                enemy.type,
                { damage: this.getEnemyProjectileDamage(enemy.type) }
            );
        }
    }

    /**
     * Fire pattern for Shadow enemies - Stealth shots with unpredictable timing
     * @param {Enemy} enemy - Shadow enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireShadowEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Shadow enemies have unpredictable firing with slight misdirection
        const misdirection = (Math.random() - 0.5) * 0.3;
        const direction = new Vector2(
            baseDirection.x + misdirection,
            baseDirection.y + misdirection * 0.5
        ).normalize();

        // Random delay to make timing unpredictable
        const delay = Math.random() * 200;

        setTimeout(() => {
            this.projectileSystem.fireProjectile(
                enemy.position.clone(),
                direction,
                enemy.type,
                { damage: this.getEnemyProjectileDamage(enemy.type) }
            );
        }, delay);
    }

    /**
     * Default firing pattern for unknown enemy types
     * @param {Enemy} enemy - Enemy
     * @param {Vector2} playerPosition - Player's position
     */
    fireDefaultEnemyPattern(enemy, playerPosition) {
        if (!this.projectileSystem) return;

        const baseDirection = enemy.directionTo(playerPosition);

        // Add some basic inaccuracy
        const inaccuracy = (Math.random() - 0.5) * 0.4;
        const direction = new Vector2(
            baseDirection.x + inaccuracy,
            baseDirection.y + inaccuracy * 0.5
        ).normalize();

        this.projectileSystem.fireProjectile(
            enemy.position.clone(),
            direction,
            enemy.type,
            { damage: this.getEnemyProjectileDamage(enemy.type) }
        );
    }

    /**
     * Get enemy firing chance based on type
     * @param {string} enemyType - Enemy type
     * @returns {number} Firing chance (0-1)
     */
    getEnemyFiringChance(enemyType) {
        const firingChances = {
            [ENEMY_TYPES.FIRE]: 0.8,     // High firing chance - aggressive
            [ENEMY_TYPES.AIR]: 0.7,      // High firing chance - rapid shots
            [ENEMY_TYPES.CRYSTAL]: 0.6,  // Medium firing chance - precise shots
            [ENEMY_TYPES.SHADOW]: 0.5,   // Medium firing chance - stealth shots
            [ENEMY_TYPES.WATER]: 0.4,    // Lower firing chance - wave patterns
            [ENEMY_TYPES.EARTH]: 0.3,    // Lowest firing chance - heavy shots
        };

        return firingChances[enemyType] || 0.5;
    }

    /**
     * Get enemy fire cooldown based on type and wave
     * @param {string} enemyType - Enemy type
     * @returns {number} Fire cooldown in milliseconds
     */
    getEnemyFireCooldown(enemyType) {
        const baseCooldowns = {
            [ENEMY_TYPES.FIRE]: 1200,    // Fast firing
            [ENEMY_TYPES.AIR]: 1000,     // Very fast firing
            [ENEMY_TYPES.CRYSTAL]: 1800, // Medium firing
            [ENEMY_TYPES.SHADOW]: 1500,  // Medium-fast firing
            [ENEMY_TYPES.WATER]: 2200,   // Slower firing
            [ENEMY_TYPES.EARTH]: 2800,   // Slowest firing
        };

        const baseCooldown = baseCooldowns[enemyType] || 2000;

        // Reduce cooldown with higher waves (more aggressive)
        const waveReduction = Math.min(400, this.currentWave * 50);

        return Math.max(600, baseCooldown - waveReduction);
    }

    /**
     * Get enemy projectile damage based on type
     * @param {string} enemyType - Enemy type
     * @returns {number} Projectile damage
     */
    getEnemyProjectileDamage(enemyType) {
        const baseDamage = {
            [ENEMY_TYPES.FIRE]: 15,      // High damage, fast shots
            [ENEMY_TYPES.AIR]: 8,        // Low damage, rapid shots
            [ENEMY_TYPES.CRYSTAL]: 20,   // High damage, precise shots
            [ENEMY_TYPES.SHADOW]: 18,    // High damage, stealth shots
            [ENEMY_TYPES.WATER]: 10,     // Medium damage, wave shots
            [ENEMY_TYPES.EARTH]: 25,     // Very high damage, slow shots
        };

        return baseDamage[enemyType] || 12;
    }

    /**
     * Get enemies that can attack the player
     * @param {Vector2} playerPosition - Player's position
     * @returns {Array} Array of enemies that can attack
     */
    getEnemiesInAttackRange(playerPosition) {
        return this.activeEnemies.filter(enemy =>
            enemy.canAttackPlayer(playerPosition)
        );
    }

    /**
     * Check collision between enemy projectiles and player
     * @param {PlayerShip} player - Player ship
     * @returns {Array} Array of colliding enemy projectiles
     */
    checkEnemyProjectileCollisions(player) {
        return this.projectileSystem.checkPlayerCollisions(player);
    }

    /**
     * Handle collision between enemy projectile and player
     * @param {EnemyProjectile} projectile - Enemy projectile
     * @param {PlayerShip} player - Player ship
     * @returns {object} Collision result
     */
    handleEnemyProjectileCollision(projectile, player) {
        return this.projectileSystem.handlePlayerCollision(projectile, player);
    }

    /**
     * Trigger enemy attacks
     * @param {Vector2} playerPosition - Player's position
     * @returns {Array} Array of attack data from enemies
     */
    triggerEnemyAttacks(playerPosition) {
        const attacks = [];
        const attackingEnemies = this.getEnemiesInAttackRange(playerPosition);

        for (const enemy of attackingEnemies) {
            const attackData = enemy.attack(playerPosition);
            if (attackData) {
                attacks.push(attackData);
            }
        }

        return attacks;
    }

    /**
     * Get current wave status
     * @returns {object} Wave status information
     */
    getWaveStatus() {
        return {
            currentWave: this.currentWave,
            waveInProgress: this.waveInProgress,
            enemiesSpawned: this.enemiesSpawnedInWave,
            enemiesKilled: this.enemiesKilledInWave,
            totalEnemies: this.waveConfig?.totalEnemies || 0,
            activeEnemies: this.activeEnemies.length,
            totalScore: this.totalScore,
            waveProgress: this.waveConfig ? (this.enemiesSpawnedInWave / this.waveConfig.totalEnemies) : 0
        };
    }

    /**
     * Reset enemy manager (for new game)
     */
    reset() {
        // Clear all enemies
        for (const enemy of this.activeEnemies) {
            enemy.destroy();
        }
        this.activeEnemies = [];

        // Clear all bosses
        for (const boss of this.activeBosses) {
            boss.destroy();
        }
        this.activeBosses = [];
        this.currentBoss = null;
        this.bossEncounterActive = false;

        // Reset wave management
        this.currentWave = 0;
        this.waveInProgress = false;
        this.waveConfig = null;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;
        this.enemiesEscapedInWave = 0;
        this.patternSpawnCounts = {};

        // Reset timers
        this.spawnTimer = 0;
        this.waveStartTime = 0;
        this.lastBossEncounterTime = 0;

        // Clear formations
        this.formations = [];
        this.formationSpawnQueue = [];

        // Reset statistics
        this.totalEnemiesSpawned = 0;
        this.totalEnemiesKilled = 0;
        this.totalScore = 0;
        
        // Reset environment
        this.currentEnvironment = 'space';
        this.environmentalEffects = null;
        
        // Reset GameObject ID counter to start fresh
        if (typeof GameObject !== 'undefined' && GameObject.idCounter) {
            GameObject.idCounter = 0;
        }

        // Reset projectile system disabled - enemies don't fire projectiles
        // this.projectileSystem.reset();

        console.log('EnemyManager reset');
    }

    /**
     * Update canvas dimensions (for window resize)
     * @param {number} width - New canvas width
     * @param {number} height - New canvas height
     */
    updateCanvasDimensions(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;

        // Update grid dimensions
        this.gridWidth = Math.ceil(width / this.gridSize);
        this.gridHeight = Math.ceil(height / this.gridSize);

        // Update projectile system disabled - enemies don't fire projectiles
        // this.projectileSystem.updateCanvasDimensions(width, height);

        // Update all active enemies
        for (const enemy of this.activeEnemies) {
            enemy.canvasWidth = width;
            enemy.canvasHeight = height;
        }
    }

    /**
     * Get statistics for display
     * @returns {object} Statistics object
     */
    getStatistics() {
        return {
            currentWave: this.currentWave,
            totalEnemiesSpawned: this.totalEnemiesSpawned,
            totalEnemiesKilled: this.totalEnemiesKilled,
            totalScore: this.totalScore,
            activeEnemies: this.activeEnemies.length,
            activeBosses: this.activeBosses.length,
            bossEncounterActive: this.bossEncounterActive,
            formations: this.formations.length,
            killRatio: this.totalEnemiesSpawned > 0 ? (this.totalEnemiesKilled / this.totalEnemiesSpawned) : 0
        };
    }
    
    /**
     * Check for boss encounter conditions
     */
    checkBossEncounter() {
        // Don't check if boss encounter is already active or on cooldown
        if (this.bossEncounterActive) return;
        
        // Check if boss encounter cooldown is level-based
        if (this.lastBossEncounterLevel && this.levelManager && this.levelManager.currentLevel <= this.lastBossEncounterLevel) return;
        
        // Check if wave configuration indicates a boss should spawn
        if (this.waveConfig && this.waveConfig.hasBoss &&
            this.enemiesSpawnedInWave >= Math.floor(this.waveConfig.totalEnemies * 0.7)) {
            this.spawnBossFromWave();
        }
    }
    
    /**
     * Check boss warp triggers and handle warp initiation
     * @param {Boss} boss - Boss to check for warp triggers
     */
    checkBossWarpTriggers(boss) {
        // This method will be called when a boss wants to initiate a warp
        // The actual warp logic will be handled by the RealityWarpManager
        if (boss.isWarping && !boss.warpInitiated) {
            boss.warpInitiated = true;
            this.handleBossWarpInitiated(boss);
        }
    }
    
    /**
     * Handle boss warp initiation
     * @param {Boss} boss - Boss initiating the warp
     */
    handleBossWarpInitiated(boss) {
        console.log(`Boss "${boss.bossName}" initiated reality warp`);
        
        // Trigger boss warp callback if available
        if (this.bossWarpCallback) {
            this.bossWarpCallback(boss, boss.getBossWarpPrompt());
        }
    }
    
    /**
     * Handle boss warp completion
     * @param {Boss} boss - Boss that completed the warp
     */
    handleBossWarpCompleted(boss) {
        console.log(`Boss "${boss.bossName}" completed reality warp`);
        
        // Apply post-warp effects to the boss
        boss.baseSpeed *= 1.1; // Slight speed increase after warp
        boss.currentSpeed = boss.baseSpeed;
    }
    
    /**
     * Handle boss ability usage
     * @param {object} ability - Ability used by boss
     * @param {Vector2} playerPosition - Player's position when ability was used
     */
    handleBossAbilityUsed(ability, playerPosition) {
        console.log(`Boss used ability: ${ability.name} at position ${playerPosition.x}, ${playerPosition.y}`);
        
        // Additional ability effects can be implemented here
        // For example, spawning minions, creating environmental hazards, etc.
    }
    
    /**
     * Handle boss damage taken
     * @param {number} damage - Damage taken by boss
     * @param {object} result - Damage result
     */
    handleBossDamageTaken(damage, result) {
        // Additional effects when boss takes damage
        // For example, visual effects, sound effects, etc.
    }
    
    /**
     * End the current boss encounter
     */
    endBossEncounter() {
        this.bossEncounterActive = false;
        this.currentBoss = null;
        this.lastBossEncounterTime = Date.now();
        this.lastBossEncounterLevel = this.levelManager ? this.levelManager.currentLevel : 0;
        
        console.log('Boss encounter ended');
    }
    
    /**
     * Set callback for boss defeat
     * @param {function} callback - Boss defeat callback
     */
    setBossDefeatCallback(callback) {
        this.bossDefeatCallback = callback;
    }
    
    /**
     * Set callback for boss warp initiation
     * @param {function} callback - Boss warp callback
     */
    setBossWarpCallback(callback) {
        this.bossWarpCallback = callback;
    }
    
    /**
     * Get current boss encounter status
     * @returns {object} Boss encounter status
     */
    getBossEncounterStatus() {
        return {
            isActive: this.bossEncounterActive,
            currentBoss: this.currentBoss,
            lastEncounterTime: this.lastBossEncounterTime,
            lastEncounterLevel: this.lastBossEncounterLevel,
            cooldownRemaining: 'Until next level'
        };
    }
    
    /**
     * Get all active enemies (excluding bosses)
     * @returns {Array} Array of active enemies
     */
    getActiveEnemies() {
        return this.activeEnemies;
    }

    /**
     * Handle environment change from boss warp
     * @param {object} environmentConfig - Environment configuration from boss warp
     */
    handleEnvironmentChange(environmentConfig) {
        console.log('Handling environment change from boss warp:', environmentConfig);
        
        // Update current environment
        this.currentEnvironment = environmentConfig.primaryType;
        
        // Spawn enemies based on new environment
        if (this.activeBosses.length > 0) {
            const boss = this.activeBosses[0];
            this.spawnEnvironmentEnemies(environmentConfig.primaryType, boss.position, true);
        }
    }
}