/**
 * PowerUpIndicator - UI component for displaying active power-ups and their remaining duration
 * Shows power-up icons, names, and countdown timers on the game screen
 */
export class PowerUpIndicator {
    constructor() {
        // UI state
        this.isVisible = false;
        this.container = null;
        this.activePowerUps = new Map();
        
        // Visual properties
        this.position = { x: 20, y: 20 }; // Top-left corner
        this.iconSize = 32;
        this.spacing = 10;
        this.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        this.borderColor = '#ffd700';
        
        // Animation properties
        this.animationTime = 0;
        this.pulseIntensity = 0;
        
    }
    
    /**
     * Initialize the indicator (create DOM elements)
     */
    initialize() {
        if (this.container) return;
        
        // Create container element
        this.container = document.createElement('div');
        this.container.id = 'power-up-indicator';
        this.container.className = 'power-up-indicator';
        
        // Apply styles
        this.container.style.cssText = `
            position: fixed;
            top: ${this.position.y}px;
            left: ${this.position.x}px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: ${this.spacing}px;
            pointer-events: none;
            font-family: 'Arial', sans-serif;
        `;
        
        // Add to document
        document.body.appendChild(this.container);
        
    }
    
    /**
     * Show the indicator
     */
    show() {
        if (!this.container) this.initialize();
        
        this.isVisible = true;
        this.container.style.display = 'flex';
    }
    
    /**
     * Hide the indicator
     */
    hide() {
        if (!this.container) return;
        
        this.isVisible = false;
        this.container.style.display = 'none';
    }
    
    /**
     * Update active power-ups display
     * @param {Map} activePowerUps - Map of active power-ups from GenieInterface
     */
    updateActivePowerUps(activePowerUps) {
        this.activePowerUps = activePowerUps;
        
        // If indicator was hidden but now has active power-ups, show it
        if (!this.isVisible && activePowerUps.size > 0) {
            this.show();
        }
        
        this.render();
    }
    
    /**
     * Update animation and timers
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    update(deltaTime) {
        if (!this.isVisible || this.activePowerUps.size === 0) return;
        
        // Update animation timer
        this.animationTime += deltaTime / 1000;
        this.pulseIntensity = Math.sin(this.animationTime * 2) * 0.3 + 0.7;
        
        // Re-render to update timers
        this.render();
    }
    
    /**
     * Render the power-up indicators
     */
    render() {
        if (!this.container || !this.isVisible) return;
        
        // Clear existing content
        this.container.innerHTML = '';
        
        // Show the indicator if it was hidden
        if (this.container.style.display === 'none') {
            this.container.style.display = 'flex';
        }
        
        // If no active power-ups, show empty indicator (don't hide it)
        if (this.activePowerUps.size === 0) {
            // Add a placeholder message or leave empty but visible
            const placeholder = document.createElement('div');
            placeholder.className = 'power-up-placeholder';
            placeholder.style.cssText = `
                color: rgba(255, 255, 255, 0.3);
                font-size: 12px;
                padding: 5px;
                display: none; /* Hide placeholder but keep container visible */
            `;
            placeholder.textContent = 'No active power-ups';
            this.container.appendChild(placeholder);
            return;
        }
        
        // Create indicator for each active power-up
        for (const [type, powerUp] of this.activePowerUps) {
            const indicator = this.createPowerUpIndicator(powerUp);
            this.container.appendChild(indicator);
        }
    }
    
    /**
     * Create a single power-up indicator element
     * @param {PowerUp} powerUp - The power-up to create an indicator for
     * @returns {HTMLElement} The indicator element
     */
    createPowerUpIndicator(powerUp) {
        const indicator = document.createElement('div');
        indicator.className = 'power-up-item';
        
        // Get power-up display info
        const info = powerUp.getDisplayInfo();
        const timeRemaining = powerUp.timeRemaining || 0;
        const timeRemainingSeconds = Math.ceil(timeRemaining / 1000);
        const timePercentage = powerUp.getTimeRemainingPercentage();
        
        // Determine urgency color
        let urgencyColor = '#00ff88'; // Green
        if (timePercentage < 0.3) {
            urgencyColor = '#ff4444'; // Red
        } else if (timePercentage < 0.6) {
            urgencyColor = '#ffaa00'; // Orange
        }
        
        // Create indicator HTML
        indicator.innerHTML = `
            <div class="power-up-icon" style="
                width: ${this.iconSize}px;
                height: ${this.iconSize}px;
                background: linear-gradient(135deg, ${info.color}, ${info.glowColor});
                border: 2px solid ${urgencyColor};
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                box-shadow: 0 0 10px ${urgencyColor}40;
                animation: ${timePercentage < 0.3 ? 'powerUpUrgent 0.5s ease-in-out infinite alternate' : 'none'};
            ">
                ${info.icon}
            </div>
            <div class="power-up-info" style="
                margin-left: 8px;
                color: white;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                min-width: 120px;
            ">
                <div class="power-up-name" style="
                    font-size: 12px;
                    font-weight: bold;
                    color: ${urgencyColor};
                    margin-bottom: 2px;
                ">
                    ${this.formatPowerUpName(info.type)}
                </div>
                ${powerUp.duration ? `
                    <div class="power-up-timer" style="
                        font-size: 11px;
                        color: #cccccc;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    ">
                        <div class="timer-bar" style="
                            width: 60px;
                            height: 4px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 2px;
                            overflow: hidden;
                        ">
                            <div class="timer-fill" style="
                                width: ${timePercentage * 100}%;
                                height: 100%;
                                background: ${urgencyColor};
                                transition: width 0.1s ease;
                            "></div>
                        </div>
                        <span>${timeRemainingSeconds}s</span>
                    </div>
                ` : `
                    <div class="power-up-level" style="
                        font-size: 11px;
                        color: #00ff88;
                    ">
                        Active for Level
                    </div>
                `}
            </div>
        `;
        
        // Apply container styles
        indicator.style.cssText = `
            display: flex;
            align-items: center;
            background: ${this.backgroundColor};
            border: 1px solid ${this.borderColor};
            border-radius: 10px;
            padding: 8px;
            margin-bottom: 4px;
            backdrop-filter: blur(5px);
            opacity: ${this.pulseIntensity};
        `;
        
        return indicator;
    }
    
    /**
     * Format power-up type name for display
     * @param {string} type - Power-up type
     * @returns {string} Formatted name
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Wingman';
            case 'REALITY_WARP':
                return 'Reality Warp';
            case 'RAPID_FIRE':
                return 'Rapid Fire';
            case 'SHIELD':
                return 'Shield';
            case 'RICOCHET_ROUNDS':
                return 'Ricochet Rounds';
            case 'KINETIC_BOOST':
                return 'Kinetic Rounds';
            case 'LASER_ROUNDS':
                return 'Laser Rounds';
            case 'FLAME_ROUNDS':
                return 'Flame Rounds';
            case 'ICE_ROUNDS':
                return 'Ice Rounds';
            case 'PLASMA_ROUNDS':
                return 'Plasma Rounds';
            case 'SHADOW_ROUNDS':
                return 'Shadow Rounds';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    /**
     * Set position of the indicator
     * @param {number} x - X position
     * @param {number} y - Y position
     */
    setPosition(x, y) {
        this.position = { x, y };
        if (this.container) {
            this.container.style.left = `${x}px`;
            this.container.style.top = `${y}px`;
        }
    }
    
    /**
     * Destroy the indicator and clean up
     */
    destroy() {
        if (this.container) {
            this.container.remove();
            this.container = null;
        }
        this.activePowerUps.clear();
    }
}

// Add CSS animations to document head
if (!document.getElementById('power-up-indicator-styles')) {
    const style = document.createElement('style');
    style.id = 'power-up-indicator-styles';
    style.textContent = `
        @keyframes powerUpUrgent {
            from { 
                box-shadow: 0 0 10px #ff444440;
                transform: scale(1);
            }
            to { 
                box-shadow: 0 0 20px #ff4444;
                transform: scale(1.05);
            }
        }
    `;
    document.head.appendChild(style);
}
