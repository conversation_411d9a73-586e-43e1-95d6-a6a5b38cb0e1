import { PowerUpFactory } from '../systems/PowerUp.js';
import { ConsumableFactory } from '../systems/Consumable.js';

/**
 * GenieInterface - Modal interface for purchasing power-ups and reality warps between levels
 * Provides a mystical Genie-themed UI for player purchases using WISH tokens
 */
export class GenieInterface {
    constructor(tokenManager, gameEngine) {
        this.tokenManager = tokenManager;
        this.gameEngine = gameEngine;

        // UI state
        this.isVisible = false;
        this.isInitialized = false;
        this.container = null;
        
        // Available power-ups (now including weapon variants and hangar upgrades)
        this.availablePowerUps = PowerUpFactory.createAllPowerUps();
        this.activePowerUps = new Map(); // Track active power-ups by type
        this.currentWeaponVariant = 'standard';

        // Add hangar upgrades to available power-ups
        this.addHangarUpgradesToPowerUps();

        // Available environments
        this.availableEnvironments = [];
        this.selectedEnvironments = [];

        // Available consumables
        this.availableConsumables = ConsumableFactory.createAllConsumables();

        // Callbacks
        this.onPowerUpPurchased = null;
        this.onWarpPurchased = null;
        this.onEnvironmentPurchased = null;
        this.onConsumablePurchased = null;
        this.onClose = null;

        // Animation state
        this.animationFrame = null;
        this.glowAnimation = 0;

        // Feedback system
        this.feedbackMessages = [];
        this.feedbackTimeout = null;
        
    }
    
    /**
     * Initialize the Genie interface
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Create container element
            this.container = document.createElement('div');
            this.container.id = 'genie-interface';
            this.container.className = 'genie-interface hidden';
            
            // Add to document body
            document.body.appendChild(this.container);
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            
        } catch (error) {
            console.error('GenieInterface initialization error:', error);
            throw error;
        }
    }
    
    /**
     * Show the Genie interface
     */
    async show() {
        if (!this.isInitialized) {
            return;
        }

        this.isVisible = true;
        this.updatePowerUpAvailability();
        await this.updateAvailableEnvironments();
        this.render();
        this.container.classList.remove('hidden');
        this.container.classList.add('visible');

        // Start glow animation
        this.startGlowAnimation();

    }
    
    /**
     * Hide the Genie interface
     */
    hide() {
        if (!this.isVisible) return;
        
        this.isVisible = false;
        this.container.classList.remove('visible');
        this.container.classList.add('hidden');
        
        // Stop glow animation
        this.stopGlowAnimation();
        
    }
    
    /**
     * Update power-up availability based on current game state
     */
    updatePowerUpAvailability() {
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;

        console.log(`GenieInterface: Updating power-up availability with ${playerTokens} tokens`);

        this.availablePowerUps.forEach(powerUp => {
            const availability = powerUp.canPurchase(playerShip, playerTokens);
            powerUp.availability = availability;

            if (powerUp.isHangarUpgrade) {
                console.log(`Hangar upgrade ${powerUp.name}: cost=${powerUp.cost}, tokens=${playerTokens}, canPurchase=${availability.canPurchase}, reason=${availability.reason}`);
            }
        });
    }

    /**
     * Add hangar upgrades to the available power-ups list
     */
    addHangarUpgradesToPowerUps() {
        const hangarManager = this.gameEngine?.hangarManager;
        if (!hangarManager) return;

        // Get player upgrades
        const playerUpgrades = hangarManager.getUpgradeInfo('player');
        playerUpgrades.forEach(upgrade => {
            const hangarPowerUp = {
                type: `hangar_player_${upgrade.id}`,
                name: upgrade.name,
                description: `${upgrade.description} (Player Ship)`,
                cost: upgrade.nextCost,
                icon: '🚀',
                duration: null, // Permanent
                isHangarUpgrade: true,
                shipType: 'player',
                upgradeId: upgrade.id,
                currentLevel: upgrade.currentLevel,
                maxLevel: upgrade.maxLevel,
                isMaxLevel: upgrade.isMaxLevel,
                canPurchase: (playerShip, playerTokens) => {
                    if (upgrade.isMaxLevel) {
                        return { canPurchase: false, reason: 'max_level' };
                    }
                    if (playerTokens < upgrade.nextCost) {
                        return { canPurchase: false, reason: 'insufficient_tokens' };
                    }
                    return { canPurchase: true };
                }
            };
            this.availablePowerUps.push(hangarPowerUp);
        });

        // Get wingman upgrades
        const wingmanUpgrades = hangarManager.getUpgradeInfo('wingman');
        wingmanUpgrades.forEach(upgrade => {
            const hangarPowerUp = {
                type: `hangar_wingman_${upgrade.id}`,
                name: upgrade.name,
                description: `${upgrade.description} (Wingman Ship)`,
                cost: upgrade.nextCost,
                icon: '🤖',
                duration: null, // Permanent
                isHangarUpgrade: true,
                shipType: 'wingman',
                upgradeId: upgrade.id,
                currentLevel: upgrade.currentLevel,
                maxLevel: upgrade.maxLevel,
                isMaxLevel: upgrade.isMaxLevel,
                canPurchase: (playerShip, playerTokens) => {
                    if (upgrade.isMaxLevel) {
                        return { canPurchase: false, reason: 'max_level' };
                    }
                    if (playerTokens < upgrade.nextCost) {
                        return { canPurchase: false, reason: 'insufficient_tokens' };
                    }
                    return { canPurchase: true };
                }
            };
            this.availablePowerUps.push(hangarPowerUp);
        });
    }

    /**
     * Refresh hangar upgrades in the power-ups list
     */
    refreshHangarUpgrades() {
        // Remove existing hangar upgrades
        this.availablePowerUps = this.availablePowerUps.filter(powerUp => !powerUp.isHangarUpgrade);

        // Add updated hangar upgrades
        this.addHangarUpgradesToPowerUps();
    }

    /**
     * Update available environments for selection
     */
    async updateAvailableEnvironments() {
        if (!this.gameEngine.environmentTracker) {
            this.selectedEnvironments = [];
            return;
        }

        // Get random selection of 5 environments (no user exclusion)
        this.selectedEnvironments = await this.gameEngine.environmentTracker.getRandomEnvironments(5);

        // Add pricing information to environments
        this.selectedEnvironments.forEach(env => {
            env.price = this.calculateEnvironmentPrice(env);
            env.canAfford = this.tokenManager.canAfford(env.price);
        });
    }

    /**
     * Get current user ID
     * @returns {string|null} Current user ID or null if not available
     */
    getCurrentUserId() {
        // Try to get user ID from various sources
        if (this.gameEngine.orangeSDKManager && this.gameEngine.orangeSDKManager.getCurrentUser) {
            const user = this.gameEngine.orangeSDKManager.getCurrentUser();
            return user ? user.id : null;
        }

        // Fallback to a session-based ID
        if (!localStorage.getItem('sessionUserId')) {
            localStorage.setItem('sessionUserId', `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
        }
        return localStorage.getItem('sessionUserId');
    }

    /**
     * Calculate environment price based on difficulty and popularity
     * @param {object} environment - Environment object
     * @returns {number} Price in tokens
     */
    calculateEnvironmentPrice(environment) {
        const basePrices = {
            1: 5000,   // Easy (100x multiplier applied)
            2: 7500,   // Easy-Medium (100x multiplier applied)
            3: 10000,  // Medium (100x multiplier applied)
            4: 15000,  // Medium-Hard (100x multiplier applied)
            5: 20000   // Hard (100x multiplier applied)
        };

        const basePrice = basePrices[environment.difficulty] || 10000;

        // Add popularity bonus (more popular = more expensive)
        const popularityMultiplier = 1 + (environment.timesUsed * 0.01);

        return Math.round(basePrice * popularityMultiplier);
    }
    
    /**
     * Render the Genie interface
     */
    render() {
        if (!this.container) return;

        // Refresh hangar upgrades to get latest data
        this.refreshHangarUpgrades();

        // Update power-up availability before rendering
        this.updatePowerUpAvailability();

        const playerTokens = this.tokenManager.getBalance();
        
        this.container.innerHTML = `
            <div class="genie-modal">
                <div class="genie-backdrop" id="genie-backdrop"></div>
                <div class="genie-content">
                    <div class="genie-header">
                        <div class="genie-character">
                            <div class="genie-lamp">🪔</div>
                            <div class="genie-smoke"></div>
                        </div>
                        <h1 class="genie-title">The Mysterious Genie</h1>
                        <p class="genie-subtitle">Your wishes are my command, traveler...</p>
                        <div class="token-display">
                            <span class="token-icon">✨</span>
                            <span class="token-amount">${playerTokens}</span>
                            <span class="token-label">WISH Tokens</span>
                        </div>
                    </div>
                    
                    <div class="genie-body">
                        <div class="power-ups-section">
                            <h2 class="section-title">Power-Ups & Ship Upgrades</h2>
                            <div class="power-ups-grid">
                                ${this.renderPowerUps()}
                            </div>
                        </div>

                        <div class="consumables-section">
                            <h2 class="section-title">Tactical Consumables</h2>
                            <div class="consumables-grid">
                                ${this.renderConsumables()}
                            </div>
                        </div>

                        <div class="environments-section">
                            <h2 class="section-title">Mystical Environments</h2>
                            <div class="environments-grid">
                                ${this.renderEnvironments()}
                            </div>
                        </div>
                    </div>
                    
                    <div class="genie-footer">
                        <button id="genie-close-btn" class="genie-button secondary">
                            Continue Journey
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Set up button event listeners
        this.setupButtonEventListeners();
    }
    
    /**
     * Render power-up cards
     */
    renderPowerUps() {
        return this.availablePowerUps.map(powerUp => {
            
            const availability = powerUp.availability || { canPurchase: false, reason: 'unknown' };
            const isWeaponVariant = powerUp.weaponVariant !== undefined;
            const isActive = isWeaponVariant ?
                (this.gameEngine.playerShip?.weaponSystem?.currentVariant === powerUp.weaponVariant) :
                this.activePowerUps.has(powerUp.type);
            const canPurchase = availability.canPurchase && !isActive;
            
            let statusClass = '';
            let statusText = '';
            let buttonText = `Purchase (${powerUp.cost} ✨)`;
            
            if (isActive) {
                statusClass = isWeaponVariant ? 'equipped' : 'active';
                statusText = isWeaponVariant ? 'Equipped' : 'Active';
                buttonText = isWeaponVariant ? 'Equipped' : 'Already Active';
            } else if (!availability.canPurchase) {
                statusClass = 'unavailable';
                if (availability.reason === 'insufficient_tokens') {
                    statusText = 'Insufficient Tokens';
                    buttonText = `Need ${powerUp.cost - this.tokenManager.getBalance()} more ✨`;
                } else if (availability.reason === 'already_equipped') {
                    statusText = 'Already Equipped';
                    buttonText = 'Already Equipped';
                } else {
                    statusText = 'Unavailable';
                    buttonText = 'Cannot Purchase';
                }
            }
            
            // For weapon variants, show effectiveness information
            const effectivenessInfo = isWeaponVariant ? this.renderEffectiveness(powerUp.weaponVariant) : '';
            
            // Special handling for hangar upgrades
            if (powerUp.isHangarUpgrade) {
                const levelInfo = powerUp.isMaxLevel ?
                    `<div class="upgrade-level max-level">MAX LEVEL (${powerUp.currentLevel}/${powerUp.maxLevel})</div>` :
                    `<div class="upgrade-level">Level ${powerUp.currentLevel}/${powerUp.maxLevel}</div>`;

                return `
                    <div class="power-up-card hangar-upgrade ${statusClass}" data-power-up="${powerUp.type}">
                        <div class="power-up-icon">${powerUp.icon}</div>
                        <h3 class="power-up-name">${powerUp.name}</h3>
                        <p class="power-up-description">${powerUp.description}</p>
                        ${levelInfo}
                        <div class="power-up-details">
                            <div class="power-up-cost">
                                <span class="cost-amount">${powerUp.isMaxLevel ? 'MAXED' : powerUp.cost}</span>
                                ${!powerUp.isMaxLevel ? '<span class="cost-icon">✨</span>' : ''}
                            </div>
                            <div class="power-up-duration">
                                Permanent Upgrade
                            </div>
                        </div>
                        <div class="power-up-status">${statusText}</div>
                        <button class="power-up-button hangar-upgrade-btn ${canPurchase ? 'primary' : 'disabled'}"
                                data-power-up="${powerUp.type}"
                                data-upgrade-id="${powerUp.upgradeId}"
                                data-ship-type="${powerUp.shipType}"
                                ${!canPurchase ? 'disabled' : ''}>
                            ${buttonText}
                        </button>
                    </div>
                `;
            }

            // Regular power-up rendering
            return `
                <div class="power-up-card ${statusClass} ${isWeaponVariant ? 'weapon-variant' : ''}" data-power-up="${powerUp.type}">
                    <div class="power-up-icon">${powerUp.icon}</div>
                    <h3 class="power-up-name">${this.formatPowerUpName(powerUp.type)}</h3>
                    <p class="power-up-description">${powerUp.description}</p>
                    ${effectivenessInfo ? `<div class="power-up-effectiveness">${effectivenessInfo}</div>` : ''}
                    <div class="power-up-details">
                        <div class="power-up-cost">
                            <span class="cost-amount">${powerUp.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="power-up-duration">
                            ${powerUp.duration ? `${Math.ceil(powerUp.duration / 1000)}s` : 'Lasts for this Level'}
                        </div>
                    </div>
                    <div class="power-up-status">${statusText}</div>
                    <button class="power-up-button ${canPurchase ? 'primary' : 'disabled'}"
                            data-power-up="${powerUp.type}"
                            ${!canPurchase ? 'disabled' : ''}>
                        ${buttonText}
                    </button>
                </div>
            `;
        }).join('');
    }

    /**
     * Render consumable cards
     */
    renderConsumables() {
        const playerTokens = this.tokenManager.getBalance();
        const consumableManager = this.gameEngine.consumableManager;
        const hasConsumable = consumableManager && consumableManager.hasConsumable();

        return this.availableConsumables.map(consumable => {
            const availability = consumable.canPurchase(playerTokens);
            const canPurchase = availability.canPurchase && !hasConsumable;

            let statusText = '';
            let statusClass = '';

            if (hasConsumable) {
                statusText = 'Inventory Full';
                statusClass = 'status-warning';
            } else if (!availability.canPurchase) {
                statusText = availability.reason === 'insufficient_tokens' ? 'Insufficient Tokens' : 'Unavailable';
                statusClass = 'status-error';
            } else {
                statusText = 'Available';
                statusClass = 'status-available';
            }

            const displayInfo = consumable.getDisplayInfo();

            return `
                <div class="consumable-card ${canPurchase ? 'purchasable' : 'disabled'}">
                    <div class="consumable-header">
                        <div class="consumable-icon" style="
                            background: linear-gradient(135deg, ${displayInfo.color}, ${displayInfo.glowColor});
                            color: white;
                            font-size: 24px;
                        ">
                            ${displayInfo.icon}
                        </div>
                        <div class="consumable-info">
                            <h3 class="consumable-name">${displayInfo.name}</h3>
                            <div class="consumable-cost">
                                <span class="cost-amount">${displayInfo.cost}</span>
                                <span class="cost-currency">✨ WISH</span>
                            </div>
                        </div>
                    </div>

                    <div class="consumable-description">
                        ${displayInfo.description}
                    </div>

                    <div class="consumable-usage">
                        <div class="usage-info">
                            <span class="usage-icon">⚡</span>
                            <span class="usage-text">Single-use tactical advantage</span>
                        </div>
                        <div class="usage-info">
                            <span class="usage-icon">⌨️</span>
                            <span class="usage-text">Press 'E' to activate in-level</span>
                        </div>
                    </div>

                    <div class="consumable-status ${statusClass}">
                        ${statusText}
                    </div>

                    <button class="consumable-button ${canPurchase ? 'primary' : 'disabled'}"
                            data-consumable="${consumable.type}"
                            ${!canPurchase ? 'disabled' : ''}>
                        ${canPurchase ? `Purchase for ${displayInfo.cost} ✨` : 'Unavailable'}
                    </button>
                </div>
            `;
        }).join('');
    }

    /**
     * Render hangar upgrades
     */
    renderHangarUpgrades() {
        const hangarManager = this.gameEngine?.hangarManager;
        if (!hangarManager) {
            return `
                <div class="hangar-unavailable">
                    <div class="hangar-icon">🔧</div>
                    <p>Hangar system not available</p>
                </div>
            `;
        }

        const upgrades = hangarManager.getUpgradeInfo(this.selectedHangarTab);
        if (!upgrades || upgrades.length === 0) {
            return `
                <div class="no-upgrades">
                    <div class="no-upgrades-icon">⚙️</div>
                    <p>No upgrades available for ${this.selectedHangarTab} ship</p>
                </div>
            `;
        }

        return upgrades.map(upgrade => {
            const isMaxLevel = upgrade.isMaxLevel;
            const canAfford = upgrade.canAfford && !isMaxLevel;
            const playerTokens = this.tokenManager.getBalance();

            let statusClass = '';
            let buttonText = '';

            if (isMaxLevel) {
                statusClass = 'max-level';
                buttonText = 'MAXED';
            } else if (canAfford) {
                statusClass = 'available';
                buttonText = `Upgrade (${upgrade.nextCost} ✨)`;
            } else {
                statusClass = 'insufficient-tokens';
                buttonText = `Need ${upgrade.nextCost} ✨`;
            }

            return `
                <div class="hangar-upgrade-card ${statusClass}">
                    <div class="upgrade-header">
                        <h3 class="upgrade-name">${upgrade.name}</h3>
                        <div class="upgrade-level">Level ${upgrade.currentLevel}/${upgrade.maxLevel}</div>
                    </div>
                    <div class="upgrade-body">
                        <p class="upgrade-description">${upgrade.description}</p>
                        <div class="upgrade-effects">
                            Affects: ${upgrade.affects.join(', ')}
                        </div>
                    </div>
                    <div class="upgrade-footer">
                        <div class="upgrade-cost">
                            ${isMaxLevel ? '✨ MAXED' : `✨ ${upgrade.nextCost}`}
                        </div>
                        <button class="genie-button hangar-upgrade-btn ${canAfford && !isMaxLevel ? 'primary' : 'disabled'}"
                                data-upgrade-id="${upgrade.id}"
                                data-ship-type="${this.selectedHangarTab}"
                                ${!canAfford || isMaxLevel ? 'disabled' : ''}>
                            ${buttonText}
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Render environment cards
     */
    renderEnvironments() {
        if (!this.selectedEnvironments || this.selectedEnvironments.length === 0) {
            return `
                <div class="no-environments">
                    <div class="no-environments-icon">🌌</div>
                    <p>No mystical environments available at this time.</p>
                    <p class="hint">Create your own environment using Reality Warp!</p>
                </div>
            `;
        }

        return this.selectedEnvironments.map(env => {
            const canAfford = env.canAfford;
            const popularityText = env.timesUsed > 0 ? `Used ${env.timesUsed} times` : 'New environment';

            let statusClass = '';
            let statusText = '';
            let buttonText = `Purchase (${env.price} ✨)`;

            if (!canAfford) {
                statusClass = 'insufficient-tokens';
                statusText = 'Insufficient tokens';
                buttonText = `Need ${env.price} ✨`;
            } else {
                statusClass = 'available';
                statusText = 'Available';
            }

            // Ensure environment has proper name and description
            const envName = env.name || 'Custom Environment';
            const envDescription = env.description || 'A mystical environment created by a player.';
            const envTags = env.tags || [];
            const envCreator = env.creatorUserId || 'unknown';

            return `
                <div class="environment-card ${statusClass}">
                    <div class="environment-header">
                        <h3 class="environment-name">${envName}</h3>
                    </div>
                    <div class="environment-body">
                        <p class="environment-description">${envDescription}</p>
                        <div class="environment-tags">
                            ${envTags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                        <div class="environment-stats">
                            <span class="popularity">${popularityText}</span>
                            <span class="creator">by ${this.formatCreatorName(envCreator)}</span>
                        </div>
                    </div>
                    <div class="environment-footer">
                        <div class="environment-status ${statusClass}">${statusText}</div>
                        <button class="genie-button environment-purchase-btn ${canAfford ? 'primary' : 'disabled'}"
                                data-environment-id="${env.id}"
                                ${!canAfford ? 'disabled' : ''}>
                            ${buttonText}
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Format creator name for display
     * @param {string} creatorUserId - Creator user ID
     * @returns {string} Formatted creator name
     */
    formatCreatorName(creatorUserId) {
        // Extract a readable name from user ID
        if (creatorUserId.startsWith('user_')) {
            const parts = creatorUserId.split('_');
            if (parts.length >= 3) {
                return `Player ${parts[2].substring(0, 6)}`;
            }
        }
        return 'Anonymous';
    }

    /**
     * Format power-up type name for display
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Wingman';
            case 'REALITY_WARP':
                return 'Reality Warp';
            case 'KINETIC_BOOST':
                return 'Kinetic Boost';
            case 'LASER_ROUNDS':
                return 'Laser Rounds';
            case 'FLAME_ROUNDS':
                return 'Flame Rounds';
            case 'ICE_ROUNDS':
                return 'Ice Rounds';
            case 'PLASMA_ROUNDS':
                return 'Plasma Rounds';
            case 'SHADOW_ROUNDS':
                return 'Shadow Rounds';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }

    /**
     * Render effectiveness indicators for weapon variants
     */
    renderEffectiveness(variantType) {
        const weaponSystem = this.gameEngine.playerShip?.weaponSystem;
        if (!weaponSystem) return '';

        const variantProps = weaponSystem.variantProperties[variantType];
        if (!variantProps || !variantProps.effectivenessMultipliers) return '';

        const effectiveness = Object.entries(variantProps.effectivenessMultipliers)
            .map(([enemyType, multiplier]) => {
                const effectivenessText = multiplier >= 2.0 ? 'Very Effective' : 'Effective';
                const enemyIcon = this.getEnemyTypeIcon(enemyType);
                return `<span class="effectiveness-item" title="${effectivenessText} vs ${enemyType}">
                    ${enemyIcon} ${multiplier}x
                </span>`;
            })
            .join('');

        return effectiveness ? `<div class="effectiveness-list">${effectiveness}</div>` : '';
    }

    /**
     * Get icon for enemy type
     */
    getEnemyTypeIcon(enemyType) {
        const icons = {
            'water': '💧',
            'fire': '🔥',
            'air': '💨',
            'earth': '🌍',
            'crystal': '💎',
            'shadow': '🌑'
        };
        return icons[enemyType] || '⚔️';
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Handle escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });
    }
    
    /**
     * Set up button event listeners after rendering
     */
    setupButtonEventListeners() {
        // Close button
        const closeBtn = this.container.querySelector('#genie-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }
        
        // Backdrop click to close
        const backdrop = this.container.querySelector('#genie-backdrop');
        if (backdrop) {
            backdrop.addEventListener('click', () => this.close());
        }
        
        // Power-up purchase buttons
        const powerUpButtons = this.container.querySelectorAll('.power-up-button:not(.disabled)');
        powerUpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const powerUpType = e.target.dataset.powerUp;
                this.handlePowerUpPurchase(powerUpType);
            });
        });

        // Consumable purchase buttons
        const consumableButtons = this.container.querySelectorAll('.consumable-button:not(.disabled)');
        consumableButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const consumableType = e.target.dataset.consumable;
                this.handleConsumablePurchase(consumableType);
            });
        });

        // Hangar upgrade buttons (now integrated into power-up buttons)
        const hangarUpgradeButtons = this.container.querySelectorAll('.hangar-upgrade-btn:not(.disabled)');
        hangarUpgradeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const upgradeId = e.target.dataset.upgradeId;
                const shipType = e.target.dataset.shipType;
                this.handleHangarUpgrade(upgradeId, shipType);
            });
        });

        // Environment purchase buttons
        const environmentButtons = this.container.querySelectorAll('.environment-purchase-btn:not(.disabled)');
        environmentButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const environmentId = e.target.dataset.environmentId;
                this.handleEnvironmentPurchase(environmentId);
            });
        });
        
        // Warp purchase buttons
        const warpButtons = this.container.querySelectorAll('.warp-button:not(.disabled)');
        warpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const powerUpType = e.target.dataset.powerUp;
                this.handlePowerUpPurchase(powerUpType);
            });
        });
    }
    
    /**
     * Handle power-up purchase
     */
    async handlePowerUpPurchase(powerUpType) {
        const powerUp = this.availablePowerUps.find(p => p.type === powerUpType);
        if (!powerUp) {
            return;
        }

        // Check if this is a hangar upgrade
        if (powerUp.isHangarUpgrade) {
            this.handleHangarUpgrade(powerUp.upgradeId, powerUp.shipType);
            return;
        }

        // Check if this is a weapon variant
        const isWeaponVariant = powerUp.weaponVariant !== undefined;

        // Check if purchase is valid
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;
        const availability = powerUp.canPurchase(playerShip, playerTokens);

        if (!availability.canPurchase) {

            // Show appropriate error message
            let errorMessage = isWeaponVariant ? 'Cannot equip weapon variant' : 'Cannot purchase power-up';
            switch (availability.reason) {
                case 'insufficient_tokens':
                    errorMessage = `Not enough WISH tokens! Need ${powerUp.cost}, have ${playerTokens}`;
                    break;
                case 'already_active':
                    errorMessage = 'Power-up is already active!';
                    break;
                case 'already_equipped':
                    errorMessage = 'This weapon variant is already equipped!';
                    break;
                default:
                    errorMessage = `Cannot purchase: ${availability.reason}`;
            }

            this.showFeedback(errorMessage, 'error');
            return;
        }

        // Initialize spendResult to null to ensure it's defined in all code paths
        let spendResult = null;

        // For Reality Warp, defer token spending until after user confirms their idea
        if (powerUpType !== 'REALITY_WARP') {
            // Spend tokens for other power-ups immediately
            const purchaseType = isWeaponVariant ? `weapon_variant_${powerUpType.toLowerCase()}` : `power_up_${powerUpType.toLowerCase()}`;
            spendResult = this.tokenManager.spendTokens(powerUp.cost, purchaseType);
            if (!spendResult.success) {
                this.showFeedback('Transaction failed! Please try again.', 'error');
                return;
            }
        }

        // Apply power-up (now async)
        const applyResult = await powerUp.apply(playerShip);
        if (!applyResult) {
            // Refund tokens if they were spent
            if (spendResult && spendResult.success) {
                this.tokenManager.awardTokens(powerUp.cost, 'power_up_refund');
            }
            this.showFeedback(isWeaponVariant ? 'Failed to equip weapon variant. Tokens refunded.' : 'Power-up activation failed! Tokens refunded.', 'error');
            return;
        }

        // Track active power-up or current weapon variant
        if (isWeaponVariant) {
            // Remove any previous weapon power-up from activePowerUps
            const weaponPowerUpTypes = ['KINETIC_BOOST', 'LASER_ROUNDS', 'FLAME_ROUNDS', 'ICE_ROUNDS', 'PLASMA_ROUNDS', 'SHADOW_ROUNDS'];
            weaponPowerUpTypes.forEach(type => {
                if (type !== powerUpType && this.activePowerUps.has(type)) {
                    this.activePowerUps.delete(type);
                }
            });
            
            this.currentWeaponVariant = powerUp.weaponVariant;
            // Also add weapon power-ups to activePowerUps for display
            this.activePowerUps.set(powerUpType, powerUp);
        } else {
            this.activePowerUps.set(powerUpType, powerUp);
        }

        // Check if this is a Reality Warp and collect user input
        if (powerUpType === 'REALITY_WARP') {
            // Show input dialog for Reality Warp idea
            // Note: The Reality Warp execution now happens inside collectRealityWarpIdea
            const userIdea = await this.collectRealityWarpIdea();
            if (!userIdea) {
                // User cancelled or provided empty input
                this.showFeedback('Reality Warp cancelled.', 'info');
                return;
            }
            
            // Spend tokens for Reality Warp after user confirms their idea
            spendResult = this.tokenManager.spendTokens(powerUp.cost, `power_up_${powerUpType.toLowerCase()}`);
            if (!spendResult.success) {
                this.showFeedback('Transaction failed! Please try again.', 'error');
                return;
            }
            
            // If we get here, the Reality Warp was successful
            this.showFeedback('Reality Warp activated with your idea!', 'success');
        }

        // Update UI
        this.updatePowerUpAvailability();
        this.render();

        // Trigger callback
        if (this.onPowerUpPurchased) {
            this.onPowerUpPurchased(powerUp, spendResult);
        }


        // Show success feedback
        const powerUpName = this.formatPowerUpName(powerUpType);
        const actionText = isWeaponVariant ? 'equipped' : 'activated';
        this.showFeedback(`${powerUpName} ${actionText}! (-${powerUp.cost} tokens)`, 'success');
    }

    /**
     * Handle consumable purchase
     */
    async handleConsumablePurchase(consumableType) {
        const consumableManager = this.gameEngine.consumableManager;
        if (!consumableManager) {
            this.showFeedback('Consumable system not available', 'error');
            return;
        }

        // Attempt to purchase the consumable
        const purchaseResult = consumableManager.purchaseConsumable(consumableType);

        if (purchaseResult.success) {
            // Show success feedback
            const consumable = purchaseResult.consumable;
            this.showFeedback(`${consumable.name} purchased! Press 'E' to use in-level. (-${consumable.cost} tokens)`, 'success');

            // Re-render to update the display
            this.render();

            // Trigger callback
            if (this.onConsumablePurchased) {
                this.onConsumablePurchased(consumable, purchaseResult.transaction);
            }
        } else {
            // Show error feedback
            let errorMessage = 'Failed to purchase consumable';
            switch (purchaseResult.reason) {
                case 'inventory_full':
                    errorMessage = 'You can only carry one consumable at a time. Use your current consumable first.';
                    break;
                case 'insufficient_tokens':
                    errorMessage = 'Not enough WISH tokens!';
                    break;
                case 'invalid_consumable':
                    errorMessage = 'Unknown consumable type';
                    break;
                case 'transaction_failed':
                    errorMessage = 'Payment processing failed';
                    break;
                default:
                    errorMessage = purchaseResult.message || 'Purchase failed';
            }

            this.showFeedback(errorMessage, 'error');
        }
    }

    /**
     * Handle hangar upgrade purchase
     * @param {string} upgradeId - Upgrade ID to purchase
     * @param {string} shipType - Ship type ('player' or 'wingman')
     */
    handleHangarUpgrade(upgradeId, shipType) {
        const hangarManager = this.gameEngine?.hangarManager;
        if (!hangarManager) {
            console.error('Hangar manager not available');
            return;
        }

        let result;
        if (shipType === 'player') {
            result = hangarManager.purchasePlayerUpgrade(upgradeId);
        } else {
            result = hangarManager.purchaseWingmanUpgrade(upgradeId);
        }

        if (result.success) {
            // Show success feedback
            this.showUpgradeSuccess(result, shipType);

            // Re-render to update the display
            this.render();
        } else {
            // Show error feedback
            this.showUpgradeError(result);
        }
    }

    /**
     * Show upgrade success feedback
     * @param {object} result - Purchase result
     * @param {string} shipType - Ship type
     */
    showUpgradeSuccess(result, shipType) {
        // Create a temporary success message
        const message = document.createElement('div');
        message.className = 'upgrade-success-message';
        message.innerHTML = `
            <div class="success-content">
                ✅ ${result.upgradeId.replace('_', ' ').toUpperCase()} upgraded to Level ${result.newLevel}!
                <br>
                <small>Cost: ${result.cost} ✨ WISH tokens</small>
            </div>
        `;

        // Add to container
        this.container.appendChild(message);

        // Remove after 3 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    /**
     * Show upgrade error feedback
     * @param {object} result - Purchase result
     */
    showUpgradeError(result) {
        console.error('Upgrade purchase failed:', result.reason);

        // Create a temporary error message
        const message = document.createElement('div');
        message.className = 'upgrade-error-message';
        message.innerHTML = `
            <div class="error-content">
                ❌ Upgrade failed: ${result.reason.replace('_', ' ')}
            </div>
        `;

        // Add to container
        this.container.appendChild(message);

        // Remove after 3 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    /**
     * Handle environment purchase
     * @param {string} environmentId - ID of environment being purchased
     */
    async handleEnvironmentPurchase(environmentId) {
        if (!this.gameEngine.environmentTracker) {
            console.error('Environment tracker not available');
            return;
        }

        const environment = await this.gameEngine.environmentTracker.getEnvironment(environmentId);
        if (!environment) {
            console.error('Environment not found:', environmentId);
            return;
        }

        const cost = environment.price;
        const playerTokens = this.tokenManager.getBalance();

        if (playerTokens < cost) {
            this.showFeedback('Insufficient tokens for environment purchase', 'error');
            return;
        }

        try {
            // Deduct tokens
            const spendResult = this.tokenManager.spendTokens(cost, 'environment_purchase', {
                environmentId: environmentId,
                environmentName: environment.name
            });

            if (!spendResult.success) {
                console.error('Failed to spend tokens for environment purchase');
                this.showFeedback('Failed to purchase environment', 'error');
                return;
            }

            // Record the purchase in the environment tracker
            const currentUserId = this.getCurrentUserId();
            // Ensure cost is a valid number before recording the purchase
            const validCost = Number(cost);
            if (isNaN(validCost) || validCost <= 0) {
                console.error('Invalid environment cost:', cost);
                this.showFeedback('Error: Invalid environment cost', 'error');
                return;
            }
            
            const purchaseResult = await this.gameEngine.environmentTracker.recordEnvironmentPurchase(environmentId, currentUserId, validCost);
            
            if (!purchaseResult) {
                // If recording the purchase failed, refund the tokens
                this.tokenManager.awardTokens(validCost, 'environment_purchase_refund');
                this.showFeedback('Failed to record environment purchase. Tokens refunded.', 'error');
                return;
            }

            // Process creator reward using RewardManager
            if (this.gameEngine.rewardManager) {
                const rewardResult = this.gameEngine.rewardManager.processCreatorReward(
                    environment.creatorUserId,
                    currentUserId,
                    environmentId,
                    cost,
                    environment
                );

                if (rewardResult.success) {
                    console.log(`Creator reward processed: ${rewardResult.creatorReward} tokens for ${environment.creatorUserId}`);
                } else {
                    console.error('Failed to process creator reward:', rewardResult.error);
                }
            }

            // Apply the environment to the next level
            await this.applyEnvironmentToNextLevel(environment);

            // Trigger callback if set
            if (this.onEnvironmentPurchased) {
                this.onEnvironmentPurchased(environment, cost);
            }

            // Update UI
            this.updateAvailableEnvironments();
            this.render();

            // Show success feedback
            this.showFeedback(`Environment "${environment.name}" purchased! (-${cost} tokens)`, 'success');

        } catch (error) {
            console.error('Error purchasing environment:', error);
            this.showFeedback('Error purchasing environment', 'error');
        }
    }

    /**
     * Apply purchased environment to the next level
     * @param {object} environment - Environment to apply
     */
    async applyEnvironmentToNextLevel(environment) {
        try {
            // Store environment in RealityWarpManager as pending for next level
            if (this.gameEngine.realityWarpManager) {
                // Create a complete copy of the environment data with proper structure
                const environmentData = {
                    type: environment.type || 'custom',
                    name: environment.name || 'Custom Environment',
                    description: environment.description || 'Custom environment from Genie',
                    imagePrompt: environment.imagePrompt || '',
                    gameplayModifiers: environment.gameplayModifiers || {},
                    imageFileName: environment.imageFileName || null,
                    imageUrl: environment.imageUrl || null,
                    // Ensure imageData has the correct structure expected by the game engine
                    imageData: environment.imageData || {
                        images: []
                    }
                };

                // Process image data to ensure it has the correct structure
                if (environment.imageUrl && (!environment.imageData || !environment.imageData.images)) {
                    // If we have a URL but no imageData structure, create one
                    environmentData.imageData = {
                        images: [{
                            url: environment.imageUrl,
                            localUrl: environment.imageUrl.startsWith('/api/') ? `http://localhost:3001${environment.imageUrl}` : environment.imageUrl
                        }]
                    };
                } else if (environment.imageData && environment.imageData.images && environment.imageData.images[0]) {
                    // Ensure the image data has the correct URL structure
                    const imageData = environment.imageData.images[0];
                    const imageUrl = imageData.localUrl || imageData.url;
                    // Ensure we use the full server URL
                    environmentData.imageUrl = imageUrl.startsWith('/api/') ? `http://localhost:3001${imageUrl}` : imageUrl;
                    
                    // Create a proper copy of the imageData structure
                    environmentData.imageData = {
                        images: [{
                            ...imageData,
                            url: imageData.url,
                            localUrl: imageUrl
                        }]
                    };
                }

                // Store as pending environment using the RealityWarpManager's method
                this.gameEngine.realityWarpManager.pendingEnvironment = environmentData;

                console.log('Environment set for next level:', environment.name, 'with image URL:', environmentData.imageUrl);
            }
        } catch (error) {
            console.error('Error applying environment to next level:', error);
        }
    }

    /**
     * Collect user's idea for Reality Warp
     * @returns {Promise<string|null>} User's idea or null if cancelled
     */
    async collectRealityWarpIdea() {
        return new Promise((resolve) => {
            let overlay = null;
            let textarea = null;
            let confirmButton = null;
            let cancelButton = null;
            let isGenerating = false;
            
            const cleanup = () => {
                if (overlay && overlay.parentNode) {
                    document.body.removeChild(overlay);
                }
                const style = document.querySelector('style[data-reality-warp-style]');
                if (style) {
                    document.head.removeChild(style);
                }
            };
            
            const showLoadingState = () => {
                if (!overlay) return;
                
                const modal = overlay.querySelector('.reality-warp-input-modal');
                modal.innerHTML = `
                    <div class="reality-warp-input-header">
                        <h3>Generating Environment...</h3>
                        <p>Please wait while we create your custom environment...</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Creating your reality...</div>
                    </div>
                `;
                
                // Add loading styles
                const loadingStyle = document.createElement('style');
                loadingStyle.textContent = `
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #475569;
                        border-top: 4px solid #6366f1;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 20px auto;
                    }
                    .loading-text {
                        text-align: center;
                        color: #cbd5e1;
                        font-size: 14px;
                        margin-top: 10px;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(loadingStyle);
            };
            
            // Create modal overlay
            overlay = document.createElement('div');
            overlay.className = 'reality-warp-input-overlay';
            overlay.innerHTML = `
                <div class="reality-warp-input-modal">
                    <div class="reality-warp-input-header">
                        <h3>Reality Warp</h3>
                        <p>Describe the environment you want to create for the next level:</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <textarea
                            id="reality-warp-idea"
                            class="reality-warp-textarea"
                            placeholder="e.g., A mystical forest with floating islands and ancient ruins..."
                            rows="4"
                            maxlength="200"
                        ></textarea>
                        <div class="reality-warp-input-footer">
                            <button id="reality-warp-cancel" class="genie-button secondary">Cancel</button>
                            <button id="reality-warp-confirm" class="genie-button primary">Create Reality</button>
                        </div>
                    </div>
                </div>
            `;
            
            // Add styles for the modal
            const style = document.createElement('style');
            style.setAttribute('data-reality-warp-style', 'true');
            style.textContent = `
                .reality-warp-input-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                .reality-warp-input-modal {
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    border: 2px solid #6366f1;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
                }
                .reality-warp-input-header h3 {
                    color: #818cf8;
                    margin: 0 0 8px 0;
                    font-size: 24px;
                    text-align: center;
                }
                .reality-warp-input-header p {
                    color: #cbd5e1;
                    margin: 0 0 16px 0;
                    text-align: center;
                    font-size: 14px;
                }
                .reality-warp-textarea {
                    width: 100%;
                    background: rgba(30, 41, 59, 0.8);
                    border: 1px solid #475569;
                    border-radius: 8px;
                    color: #e2e8f0;
                    padding: 12px;
                    font-size: 14px;
                    resize: vertical;
                    margin-bottom: 16px;
                }
                .reality-warp-textarea:focus {
                    outline: none;
                    border-color: #6366f1;
                    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
                }
                .reality-warp-input-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                }
                .genie-button {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .genie-button.primary {
                    background: #6366f1;
                    color: white;
                }
                .genie-button.primary:hover {
                    background: #4f46e5;
                }
                .genie-button.secondary {
                    background: #475569;
                    color: #e2e8f0;
                }
                .genie-button.secondary:hover {
                    background: #334155;
                }
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #475569;
                    border-top: 4px solid #6366f1;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 20px auto;
                }
                .loading-text {
                    text-align: center;
                    color: #cbd5e1;
                    font-size: 14px;
                    margin-top: 10px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            
            // Add to document
            document.head.appendChild(style);
            document.body.appendChild(overlay);
            
            // Get elements
            textarea = document.getElementById('reality-warp-idea');
            confirmButton = document.getElementById('reality-warp-confirm');
            cancelButton = document.getElementById('reality-warp-cancel');
            
            // Focus on textarea
            textarea.focus();
            
            
            cancelButton.addEventListener('click', () => {
                console.log('[DEBUG] Reality Warp dialog cancelled by user');
                cleanup();
                resolve(null);
            });
            
            confirmButton.addEventListener('click', async () => {
                const idea = textarea.value.trim();
                console.log('[DEBUG] Reality Warp dialog confirmed with idea:', idea);
                if (!idea) {
                    console.log('[DEBUG] No idea provided, resolving with null');
                    resolve(null);
                    cleanup();
                    return;
                }
                
                // Show loading state
                isGenerating = true;
                confirmButton.disabled = true;
                cancelButton.disabled = true;
                
                const modal = overlay.querySelector('.reality-warp-input-modal');
                modal.innerHTML = `
                    <div class="reality-warp-input-header">
                        <h3>Generating Environment...</h3>
                        <p>Please wait while we create your custom environment...</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Creating your reality...</div>
                    </div>
                `;
                
                try {
                    // Generate environment by sending user input to backend API
                    const response = await fetch('http://localhost:3001/api/generate-environment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            environmentDescription: idea
                        })
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || 'Failed to generate environment');
                    }
                    
                    const result = await response.json();
                    
                    if (result) {
                        console.log('Reality Warp executed with user idea:', idea);
                        
                        // Update the game with the generated environment
                        if (this.gameEngine && this.gameEngine.realityWarpManager) {
                            // Apply the generated environment to the game
                            this.gameEngine.realityWarpManager.applyGeneratedEnvironment(result);
                        }
                        
                        resolve(idea);
                    } else {
                        console.warn(`Reality Warp execution failed: Invalid response`);
                        this.showFeedback && this.showFeedback('Reality Warp failed: Invalid response from server', 'error');
                        resolve(null);
                    }
                } catch (error) {
                    console.error('Error during reality warp:', error);
                    this.showFeedback && this.showFeedback('Reality Warp failed due to an error', 'error');
                    resolve(null);
                } finally {
                    cleanup();
                }
            });
            
            // Handle keyboard events
            textarea.addEventListener('keydown', (e) => {
                // Allow WASDE keys and other normal typing
                if (e.key.length === 1 && !e.ctrlKey && !e.altKey && !e.metaKey) {
                    // Let normal character input proceed
                    return;
                }
                
                // Handle Enter key (but allow Shift+Enter for new line)
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    confirmButton.click();
                }
                
                // Handle Escape key
                if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelButton.click();
                }
                
                // Allow arrow keys, backspace, delete, home, end, etc.
                if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Backspace', 'Delete', 'Home', 'End', 'Tab'].includes(e.key)) {
                    return;
                }
            });
        });
    }
    
    /**
     * Close the interface
     */
    close() {
        this.hide();

        // Ensure all feedback messages are cleared when closing
        this.feedbackMessages = [];

        if (this.onClose) {
            this.onClose();
        }
    }

    /**
     * Show feedback message to user
     * @param {string} message - Message to display
     * @param {string} type - Message type ('success', 'error', 'warning')
     * @param {number} duration - Duration in milliseconds
     */
    showFeedback(message, type = 'info', duration = 3000) {
        const feedback = {
            id: Date.now(),
            message,
            type,
            timestamp: Date.now(),
            duration
        };

        this.feedbackMessages.push(feedback);
        this.renderFeedback();

        // Auto-remove after duration
        setTimeout(() => {
            this.removeFeedback(feedback.id);
        }, duration);
    }

    /**
     * Remove feedback message by ID
     * @param {number} id - Feedback message ID
     */
    removeFeedback(id) {
        this.feedbackMessages = this.feedbackMessages.filter(f => f.id !== id);
        this.renderFeedback();
    }

    /**
     * Render feedback messages
     */
    renderFeedback() {
        if (!this.container) return;

        // Remove existing feedback container
        const existingFeedback = this.container.querySelector('.genie-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Create new feedback container if there are messages
        if (this.feedbackMessages.length > 0) {
            const feedbackContainer = document.createElement('div');
            feedbackContainer.className = 'genie-feedback';
            feedbackContainer.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 300px;
            `;

            // Add each feedback message
            this.feedbackMessages.forEach(feedback => {
                const messageEl = document.createElement('div');
                messageEl.className = `feedback-message feedback-${feedback.type}`;

                const typeColors = {
                    success: { bg: 'rgba(0, 255, 136, 0.9)', border: '#00ff88' },
                    error: { bg: 'rgba(255, 68, 68, 0.9)', border: '#ff4444' },
                    warning: { bg: 'rgba(255, 170, 0, 0.9)', border: '#ffaa00' },
                    info: { bg: 'rgba(0, 255, 255, 0.9)', border: '#00ffff' }
                };

                const colors = typeColors[feedback.type] || typeColors.info;

                messageEl.style.cssText = `
                    background: ${colors.bg};
                    border: 2px solid ${colors.border};
                    border-radius: 8px;
                    padding: 12px 16px;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    animation: feedbackSlideIn 0.3s ease-out;
                    backdrop-filter: blur(5px);
                `;

                messageEl.textContent = feedback.message;
                feedbackContainer.appendChild(messageEl);
            });

            this.container.appendChild(feedbackContainer);
        }
    }
    
    /**
     * Start glow animation
     */
    startGlowAnimation() {
        if (this.animationFrame) return;
        
        const animate = () => {
            this.glowAnimation += 0.05;
            
            // Apply glow effect to lamp and tokens
            const lamp = this.container.querySelector('.genie-lamp');
            const tokenIcon = this.container.querySelector('.token-icon');
            
            if (lamp) {
                const glow = Math.sin(this.glowAnimation) * 0.5 + 0.5;
                lamp.style.filter = `drop-shadow(0 0 ${10 + glow * 10}px #ffd700)`;
            }
            
            if (tokenIcon) {
                const glow = Math.sin(this.glowAnimation + 1) * 0.5 + 0.5;
                tokenIcon.style.filter = `drop-shadow(0 0 ${5 + glow * 5}px #00ffff)`;
            }
            
            if (this.isVisible) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * Stop glow animation
     */
    stopGlowAnimation() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
    
    /**
     * Update active power-ups (call from game loop)
     */
    updateActivePowerUps(deltaTime) {
        const playerShip = this.gameEngine.playerShip;
        
        for (const [type, powerUp] of this.activePowerUps) {
            const stillActive = powerUp.update(deltaTime, playerShip);
            
            if (!stillActive) {
                this.activePowerUps.delete(type);
                console.log(`Power-up expired: ${type}`);
            }
        }
    }
    
    /**
     * Set callbacks
     */
    setOnPowerUpPurchased(callback) {
        this.onPowerUpPurchased = callback;
    }

    setOnWarpPurchased(callback) {
        this.onWarpPurchased = callback;
    }

    setOnConsumablePurchased(callback) {
        this.onConsumablePurchased = callback;
    }
    
    setOnClose(callback) {
        this.onClose = callback;
    }

    /**
     * Format power-up type name for display
     * @param {string} type - Power-up type
     * @returns {string} Formatted name
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Wingman';
            case 'REALITY_WARP':
                return 'Reality Warp';
            case 'KINETIC_BOOST':
                return 'Kinetic Boost';
            case 'LASER_ROUNDS':
                return 'Laser Rounds';
            case 'FLAME_ROUNDS':
                return 'Flame Rounds';
            case 'ICE_ROUNDS':
                return 'Ice Rounds';
            case 'PLASMA_ROUNDS':
                return 'Plasma Rounds';
            case 'SHADOW_ROUNDS':
                return 'Shadow Rounds';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        this.stopGlowAnimation();
        
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.container = null;
        this.isInitialized = false;
        
        console.log('GenieInterface destroyed');
    }
}
